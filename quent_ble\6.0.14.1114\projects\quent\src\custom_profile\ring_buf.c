/*
 * Ring_Buffer.c
 *
 *  Created on: 04-Sep-2023
 *      Author: admin
 */


#include "ring_buf.h"
uint32_t rbuf_debug = 0;
/*..........................................................................*/
void RingBuf_Init(RingBuf * const me,RingBufCtr sto_len) {

	me->end  = sto_len;
	me->head = 0U;
	me->tail = 0U;
}


/*..........................................................................*/
void RingBuf_DeInit(RingBuf * const me) {


	me->end  = RING_BUFF;
	me->head = 0U;
	me->tail = 0U;

}




/*..........................................................................*/
int32_t  RingBuf_put(RingBuf * const me, RingBufElement const el) {
    RingBufCtr head = me->head + 1U;
    if (head == me->end) {
        head = 0U;
    }
    if (head != me->tail) { /* buffer NOT full? */
        me->buf[me->head] = el; /* copy the element into the buffer */
        me->head = head; /* update the head to a *valid* index */
#ifdef DEBUG_ENABLE
			//Send_To_Gatt_Client(&el,1, 0x16, NULL); //NODO: debug log
#endif
        return true;  /* element placed in the buffer */
    }
    else {
        return false; /* element NOT placed in the buffer */
    }
}






/*..........................................................................*/
int32_t RingBuf_peek(RingBuf * const me) {
(void)me;
return 1;
}




/*..........................................................................*/
int32_t RingBuf_get(RingBuf * const me, RingBufElement *pel, int count)
{
	RingBufCtr tail = me->tail;
	Counter i = 0;
	while(i < count)
	{
		if (me->head != tail)
		{ /* ring buffer NOT empty? */
			*(pel+i) = me->buf[tail];
			++tail;
			if (tail == me->end) {
				tail = 0U;
			}
			me->tail = tail; /* update the tail to a *valid* index */
			i++;
		}
		else
		{
			return i;
		}
	}
	return i;
}
/*..........................................................................*/
void RingBuf_process_all(RingBuf * const me, RingBufHandler handler) {
	RingBufCtr tail = me->tail;
	while (me->head != tail) { /* ring buffer NOT empty? */
		(*handler)(me->buf[tail]);
		++tail;
		if (tail == me->end) {
			tail = 0U;
		}
		me->tail = tail; /* update the tail to a *valid* index */
	}
}
/*..........................................................................*/
RingBufCtr RingBuf_num_free(RingBuf * const me) {
	RingBufCtr head = me->head;
	RingBufCtr tail = me->tail;
	if (head == tail) { /* buffer empty? */
		return (RingBufCtr)(me->end - 1U);
	}
	else if (head < tail) {
		return (RingBufCtr)(tail - head - 1U);
	}
	else {
		return 0;//(RingBufCtr)(me->end + tail - head - 1U);
	}
}


