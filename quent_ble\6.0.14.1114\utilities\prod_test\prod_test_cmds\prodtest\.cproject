<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.PE" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="Production test application compiled for Debug" errorParsers="org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551" name="Debug" parent="cdt.managedbuild.config.gnu.cross.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551." name="/" resourcePath="">
						<toolChain errorParsers="" id="cdt.managedbuild.toolchain.gnu.mingw.base.286312640" name="MinGW GCC" superClass="cdt.managedbuild.toolchain.gnu.mingw.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.PE" id="cdt.managedbuild.target.gnu.platform.mingw.base.288904123" name="Debug Platform" osList="win32" superClass="cdt.managedbuild.target.gnu.platform.mingw.base"/>
							<builder buildPath="${workspace_loc:/prod_test}/DA14585" errorParsers="" id="cdt.managedbuild.tool.gnu.builder.mingw.base.671082228" keepEnvironmentInBuildfile="false" name="CDT Internal Builder" superClass="cdt.managedbuild.tool.gnu.builder.mingw.base"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="cdt.managedbuild.tool.gnu.assembler.mingw.base.841254279" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.mingw.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1398420781" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.archiver.mingw.base.79360929" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base.1561500777" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base">
								<option id="gnu.cpp.compiler.option.optimization.level.302141467" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.2125221812" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="cdt.managedbuild.tool.gnu.c.compiler.mingw.base.750439076" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.mingw.base">
								<option id="gnu.c.compiler.option.include.paths.442972316" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../../../../sdk/platform/include&quot;"/>
								</option>
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.c.compiler.option.optimization.level.818918245" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.793772048" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" value="gnu.c.debugging.level.max" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.dialect.std.465866831" name="Language standard" superClass="gnu.c.compiler.option.dialect.std" value="gnu.c.compiler.dialect.default" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.1567246738" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="cdt.managedbuild.tool.gnu.c.linker.mingw.base.592237979" name="MinGW C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.mingw.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.707081459" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base.848324510" name="MinGW C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="ilg.gnuarmeclipse.managedbuild.packs"/>
		</cconfiguration>
		<cconfiguration id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.639384659">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.639384659" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.PE" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="rm -rf" description="Production test application compiled for Release" errorParsers="org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.GCCErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.CWDLocator" id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.639384659" name="Release" parent="cdt.managedbuild.config.gnu.cross.exe.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.639384659." name="/" resourcePath="">
						<toolChain errorParsers="" id="cdt.managedbuild.toolchain.gnu.mingw.base.1260427512" name="MinGW GCC" superClass="cdt.managedbuild.toolchain.gnu.mingw.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.PE" id="cdt.managedbuild.target.gnu.platform.mingw.base.1572602880" name="Debug Platform" osList="win32" superClass="cdt.managedbuild.target.gnu.platform.mingw.base"/>
							<builder buildPath="${workspace_loc:/prod_test}/DA14585" errorParsers="" id="cdt.managedbuild.tool.gnu.builder.mingw.base.1359816967" keepEnvironmentInBuildfile="false" name="CDT Internal Builder" superClass="cdt.managedbuild.tool.gnu.builder.mingw.base"/>
							<tool errorParsers="org.eclipse.cdt.core.GASErrorParser" id="cdt.managedbuild.tool.gnu.assembler.mingw.base.47357860" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.mingw.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.561760205" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.archiver.mingw.base.158246323" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.mingw.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base.1636720835" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.mingw.base">
								<option id="gnu.cpp.compiler.option.optimization.level.1736989472" name="Optimization Level" superClass="gnu.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option id="gnu.cpp.compiler.option.debugging.level.2064945345" name="Debug Level" superClass="gnu.cpp.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.debugging.level.max" valueType="enumerated"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GCCErrorParser" id="cdt.managedbuild.tool.gnu.c.compiler.mingw.base.690706154" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.mingw.base">
								<option id="gnu.c.compiler.option.include.paths.789389138" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${ProjDirPath}/../../../../sdk/platform/include&quot;"/>
								</option>
								<option defaultValue="gnu.c.optimization.level.none" id="gnu.c.compiler.option.optimization.level.6112363" name="Optimization Level" superClass="gnu.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.c.optimization.level.size" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.debugging.level.928852880" name="Debug Level" superClass="gnu.c.compiler.option.debugging.level" useByScannerDiscovery="false" value="gnu.c.debugging.level.none" valueType="enumerated"/>
								<option id="gnu.c.compiler.option.dialect.std.897616807" name="Language standard" superClass="gnu.c.compiler.option.dialect.std" useByScannerDiscovery="true" value="gnu.c.compiler.dialect.default" valueType="enumerated"/>
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.1819002805" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool errorParsers="org.eclipse.cdt.core.GLDErrorParser" id="cdt.managedbuild.tool.gnu.c.linker.mingw.base.1576926190" name="MinGW C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.mingw.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.493058790" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base.308861207" name="MinGW C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.mingw.base"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="ilg.gnuarmeclipse.managedbuild.packs"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="monitor.cdt.managedbuild.target.gnu.cross.exe.210897356" name="Executable" projectType="cdt.managedbuild.target.gnu.cross.exe"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="DA14585"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.276066165;cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.276066165.;cdt.managedbuild.tool.gnu.c.compiler.mingw.base.**********;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551;cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.;cdt.managedbuild.tool.gnu.c.compiler.mingw.base.34114267;cdt.managedbuild.tool.gnu.c.compiler.input.1187567494">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.268148714;cdt.managedbuild.config.gnu.cross.exe.debug.268148714.;cdt.managedbuild.tool.gnu.c.compiler.mingw.base.763975360;cdt.managedbuild.tool.gnu.c.compiler.input.642480765">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.268148714;cdt.managedbuild.config.gnu.cross.exe.debug.268148714.;cdt.managedbuild.tool.gnu.cross.c.compiler.1701144;cdt.managedbuild.tool.gnu.c.compiler.input.55529635">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551;cdt.managedbuild.config.gnu.cross.exe.debug.268148714.77763551.;cdt.managedbuild.tool.gnu.c.compiler.mingw.base.750439076;cdt.managedbuild.tool.gnu.c.compiler.input.1567246738">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.config.gnu.cross.exe.release.883097938;cdt.managedbuild.config.gnu.cross.exe.release.883097938.;cdt.managedbuild.tool.gnu.cross.c.compiler.1179250441;cdt.managedbuild.tool.gnu.c.compiler.input.263561043">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>
