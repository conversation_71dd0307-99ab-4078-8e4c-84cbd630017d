/*
 * TransportQueue.c
 *
 *  Created on: 13-Dec-2023
 *      Author: admin
 */

#include<limits.h>
#include"Awt_types.h"
#include"TransportQueue.h"
#include"Comm_Debug.h"

// A structure to represent a queue
struct Queue {
    int front, rear, size;
    int capacity;
    tt_trigger trigger[10];
};

// function to create a queue
// of given capacity.
// It initializes size of queue as 0
static struct Queue ttrigger_queue;

void InitTriggerQueue(void)
{
	ttrigger_queue.capacity = 4;
	ttrigger_queue.front = ttrigger_queue.size = 0;
	// This is important, see the enqueue
	ttrigger_queue.rear = ttrigger_queue.capacity - 1;
	memset(&ttrigger_queue.trigger, t_invalid, sizeof(ttrigger_queue.trigger));
}

// Queue is full when size becomes
// equal to the capacity

int isTriggerFull(void)
{
    return (ttrigger_queue.size == ttrigger_queue.capacity);
}

// Queue is empty when size is 0
int isTriggerEmpty(void)
{
    return (ttrigger_queue.size == 0);
}

// Function to add an item to the queue.
// It changes rear and size
int enqueueTrigger(tt_trigger* item)
{
    if (isTriggerFull())
    {
        return 0;
    }
    ttrigger_queue.rear = (ttrigger_queue.rear + 1) % (ttrigger_queue.capacity);
    memcpy(&ttrigger_queue.trigger[ttrigger_queue.rear],item,sizeof(tt_trigger));
    ttrigger_queue.size = ttrigger_queue.size + 1;
    return 1;
}

// Function to remove an item from queue.
// It changes front and size
int dequeueTrigger(tt_trigger* item)
{
    //TODO Check if Queue is empty.
	if(isTriggerEmpty())
	{
		return 0;;
	}
	memcpy(item,&ttrigger_queue.trigger[ttrigger_queue.front],sizeof(tt_trigger));
	ttrigger_queue.front = (ttrigger_queue.front + 1) % (ttrigger_queue.capacity);
	ttrigger_queue.size = ttrigger_queue.size - 1;
    return 1;
}
