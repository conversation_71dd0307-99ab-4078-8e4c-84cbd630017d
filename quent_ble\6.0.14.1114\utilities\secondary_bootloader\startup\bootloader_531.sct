LR_1 0x07FC0000 0x00008000 {    ; load region size_region
    ER_1 +0 0x00002000 {        ; load address = execution address
        *.o (RESET, +First)
        *(InRoot$$Sections)
        startup_DA14531.o
        system_DA14531.o
    }

    ER_2 0x07FC8000 0x3000 {    ; push it to the end of SysRAM
        .ANY (+RO)
        .ANY (+RW +ZI)
        .ANY (STACK)
    }
; ***************************************************************************
; Above the address 0x07FCB000 (0x07FC8000 + 0x3000) the RAM shall not be
; used by the secondary bootloader and shall remain uninitialized. The buffer
; used by the DA14531 TRNG software mechanism must use only uninitialized
; RAM space in order to generate random seeds.
;
; The maximum size of the uninitialized RAM data, which can be fed to the
; TRNG buffer, is 0xA00 since the DA14531 flash programmer (UART version)
; leaves unattached the memory space from 0x07FCB000 to 0x7FCBA00.
; ***************************************************************************
}
