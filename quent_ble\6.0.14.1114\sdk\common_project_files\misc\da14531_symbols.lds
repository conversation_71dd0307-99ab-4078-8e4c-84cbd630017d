uECC_vli_add = 0x07f02001;
uECC_vli_sub = 0x07f02019;
uECC_vli_mult = 0x07f02035;
set_system_clocks = 0x07f02159;
set_peripheral_clocks = 0x07f0216b;
rf_workaround_init = 0x07f02183;
get_stack_usage = 0x07f02185;
rwip_eif_get_func = 0x07f0218d;
rwip_set_em_base = 0x07f0219d;
platform_initialization = 0x07f021a3;
ble_init = 0x07f02255;
ble_regs_push = 0x07f022cb;
ble_regs_pop = 0x07f02323;
platform_sleep = 0x07f02379;
rf_reinit = 0x07f02641;
smpc_check_param = 0x07f02649;
smpc_pdu_recv = 0x07f02653;
lld_sleep_compensate = 0x07f0265d;
lld_sleep_init = 0x07f02667;
lld_sleep_us_2_lpcycles = 0x07f02671;
lld_sleep_lpcycles_2_us = 0x07f0267b;
uart_flow_off = 0x07f02685;
uart_finish_transfers = 0x07f0268d;
uart_read = 0x07f02695;
uart_write = 0x07f0269d;
UART_Handler = 0x07f026a5;
uart_init = 0x07f026ad;
uart_flow_on = 0x07f026b5;
gtl_init = 0x07f026bd;
gtl_eif_init = 0x07f026c5;
gtl_eif_read_start = 0x07f026cd;
gtl_eif_read_hdr = 0x07f026d5;
gtl_eif_read_payl = 0x07f026dd;
gtl_eif_tx_done = 0x07f026e5;
gtl_eif_rx_done = 0x07f026ed;
h4tl_init = 0x07f026f5;
h4tl_read_start = 0x07f026fd;
h4tl_read_hdr = 0x07f02705;
h4tl_read_payl = 0x07f0270d;
h4tl_read_next_out_of_sync = 0x07f02715;
h4tl_out_of_sync = 0x07f0271d;
h4tl_tx_done = 0x07f02725;
h4tl_rx_done = 0x07f0272d;
ke_task_init = 0x07f02735;
ke_timer_init = 0x07f0273d;
llm_encryption_done = 0x07f02745;
nvds_get = 0x07f0274d;
nvds_del = 0x07f02755;
nvds_put = 0x07f0275d;
rwip_eif_get = 0x07f02765;
platform_reset = 0x07f0276d;
lld_test_stop = 0x07f02777;
lld_test_mode_tx = 0x07f02781;
lld_test_mode_rx = 0x07f0278b;
prf_init = 0x07f02795;
prf_add_profile = 0x07f0279f;
prf_create = 0x07f027a9;
prf_cleanup = 0x07f027b3;
prf_get_id_from_task = 0x07f027bd;
prf_get_task_from_id = 0x07f027c7;
nvds_init = 0x07f027d1;
SetSystemVars = 0x07f027d9;
dbg_init = 0x07f027e3;
dbg_platform_reset_complete = 0x07f027ed;
hci_rd_local_supp_feats_cmd_handler = 0x07f027f7;
l2cc_pdu_pack = 0x07f02807;
l2cc_pdu_unpack = 0x07f0281d;
l2c_send_lecb_message = 0x07f02835;
l2c_process_sdu = 0x07f0283f;
l2cc_pdu_recv_ind_handler = 0x07f02849;
gapc_lecb_connect_cfm_handler = 0x07f02859;
atts_l2cc_pdu_recv_handler = 0x07f02869;
attc_l2cc_pdu_recv_handler = 0x07f02873;
crypto_init = 0x07f0287d;
llm_le_adv_report_ind = 0x07f02887;
PK_PointMult = 0x07f02891;
llm_p256_start = 0x07f0289b;
llm_create_p256_key = 0x07f028a5;
llm_p256_req_handler = 0x07f028b1;
llc_le_length_effective = 0x07f028c3;
llc_le_length_conn_init = 0x07f028cf;
lld_data_tx_prog = 0x07f028db;
lld_data_tx_check = 0x07f028e7;
llc_pdu_send = 0x07f028f3;
dia_rand = 0x07f028ff;
dia_srand = 0x07f0290b;
llc_data_notif = 0x07f02917;
ba431_get_rand = 0x07f02931;
smpc_public_key_exchange_start = 0x07f0293d;
smpc_dhkey_calc_ind = 0x07f02949;
smpm_ecdh_key_create = 0x07f02955;
ble_init_arp = 0x07f02961;
co_buf_init = 0x07f02971;
co_buf_rx_free = 0x07f02a35;
co_buf_rx_buffer_get = 0x07f02a4f;
co_buf_tx_buffer_get = 0x07f02a59;
co_list_init = 0x07f02a81;
co_list_pop_front = 0x07f02a91;
co_list_flush = 0x07f02ab3;
co_list_push_back = 0x07f02ac9;
co_list_pool_init = 0x07f02aed;
co_list_push_front = 0x07f02b55;
co_list_extract = 0x07f02b71;
co_list_find = 0x07f02bc1;
co_list_merge = 0x07f02bd7;
co_list_insert_before = 0x07f02bf3;
co_list_insert_after = 0x07f02c2b;
co_list_size = 0x07f02c67;
co_bdaddr_compare = 0x07f02c7b;
co_array_reverse = 0x07f02c97;
llc_init = 0x07f02cb9;
llc_common_nb_of_pkt_comp_evt_send = 0x07f02ce5;
llc_acl_tx_data_flush = 0x07f02d05;
llc_stop = 0x07f02d6d;
llc_reset = 0x07f02db9;
llc_le_length_effective_func = 0x07f02ddb;
llc_le_length_conn_init_func = 0x07f02e75;
llc_le_enh_con_cmp_evt_send = 0x07f02ed3;
llc_le_con_cmp_evt_send = 0x07f02ff3;
llc_start = 0x07f0308f;
llc_acl_tx_data_squash = 0x07f03173;
llc_acl_tx_desc_flushed = 0x07f03215;
llc_acl_tx_data_process = 0x07f03285;
llc_discon_event_complete_send = 0x07f032d3;
llc_con_update_complete_send = 0x07f032f5;
llc_ltk_req_send = 0x07f0332f;
llc_feats_rd_event_send = 0x07f03367;
llc_version_rd_event_send = 0x07f033a3;
llc_common_cmd_complete_send = 0x07f033d5;
llc_common_cmd_status_send = 0x07f033f3;
llc_common_cmd_discard = 0x07f0340f;
llc_common_flush_occurred_send = 0x07f03417;
llc_common_enc_key_ref_comp_evt_send = 0x07f03431;
llc_common_enc_change_evt_send = 0x07f0344f;
llc_con_update_ind = 0x07f034b7;
llc_lsto_con_update = 0x07f0355b;
llc_map_update_ind = 0x07f03595;
llc_chnl_map_req_send = 0x07f03653;
llc_add_bad_chnl = 0x07f0366f;
llc_pdu_send_func = 0x07f03709;
llc_version_ind_pdu_send = 0x07f0377b;
llc_ch_map_update_pdu_send = 0x07f037d5;
llc_pause_enc_req_pdu_send = 0x07f03825;
llc_pause_enc_rsp_pdu_send = 0x07f03871;
llc_enc_req_pdu_send = 0x07f038d7;
llc_enc_rsp_pdu_send = 0x07f039a3;
llc_start_enc_rsp_pdu_send = 0x07f03a41;
llc_reject_ind_pdu_send = 0x07f03a99;
llc_con_update_pdu_send = 0x07f03b37;
llc_con_param_req_pdu_send = 0x07f03b8d;
llc_con_param_rsp_pdu_send = 0x07f03c1b;
llc_feats_req_pdu_send = 0x07f03ca9;
llc_start_enc_req_pdu_send = 0x07f03d09;
llc_terminate_ind_pdu_send = 0x07f03dbd;
llc_unknown_rsp_send_pdu = 0x07f03e31;
llc_length_req_pdu_send = 0x07f03e65;
llc_length_rsp_pdu_send = 0x07f03f31;
llc_length_ind = 0x07f03fa1;
llc_ping_req_pdu_send = 0x07f0402d;
llc_ping_rsp_pdu_send = 0x07f0405d;
llc_feats_req_ind = 0x07f0408d;
llc_feats_rsp_ind = 0x07f040f9;
llc_vers_ind_ind = 0x07f04167;
llc_terminate_ind = 0x07f041fb;
llc_pause_enc_req_ind = 0x07f04237;
llc_pause_enc_rsp_ind = 0x07f04267;
llc_enc_req_ind = 0x07f042dd;
llc_enc_rsp_ind = 0x07f0439d;
llc_start_enc_req_ind = 0x07f04449;
llc_start_enc_rsp_ind = 0x07f044b7;
llc_cntl_rcv = 0x07f0453f;
llcp_con_param_req_pdu_unpk = 0x07f045cd;
llcp_con_param_rsp_pdu_unpk = 0x07f04645;
llc_con_update_req_ind = 0x07f046bd;
llc_ch_map_req_ind = 0x07f04717;
llc_data_rcv = 0x07f04799;
llc_util_get_free_conhdl = 0x07f06bd1;
llc_util_dicon_procedure = 0x07f06c01;
llc_util_gen_skdx = 0x07f06c63;
llc_util_update_channel_map = 0x07f06c77;
llc_util_set_llcp_discard_enable = 0x07f06c89;
llc_util_set_auth_payl_to_margin = 0x07f06ca1;
llm_add_bad_chnl = 0x07f06cc9;
llc_data_notif_func = 0x07f06d05;
lld_init = 0x07f06e11;
lld_reset = 0x07f06f5d;
lld_adv_start = 0x07f06fab;
lld_adv_stop = 0x07f0710f;
lld_scan_start = 0x07f07135;
lld_scan_stop = 0x07f0727d;
lld_con_start = 0x07f072b5;
lld_move_to_master = 0x07f07647;
lld_con_update_req = 0x07f076df;
lld_con_update_after_param_req = 0x07f0775d;
lld_con_param_rsp = 0x07f07949;
lld_con_param_req = 0x07f07a45;
lld_con_stop = 0x07f07b1b;
lld_get_mode = 0x07f07b71;
lld_move_to_slave = 0x07f07b95;
lld_ch_map_ind = 0x07f07d3b;
lld_con_update_ind = 0x07f07d6b;
lld_crypt_isr = 0x07f07d79;
lld_test_mode_tx_func = 0x07f07d83;
lld_test_mode_rx_func = 0x07f07e23;
lld_test_stop_func = 0x07f07eb5;
lld_data_rx_check = 0x07f07f75;
lld_data_rx_flush = 0x07f07fb9;
lld_data_tx_check_func = 0x07f07fdf;
lld_data_tx_loop = 0x07f08085;
lld_data_tx_push = 0x07f080b3;
lld_data_tx_prog_func = 0x07f08115;
lld_data_tx_flush = 0x07f08255;
lld_evt_drift_compute = 0x07f08363;
lld_evt_elt_delete = 0x07f08449;
lld_evt_deffered_elt_handler = 0x07f08a6f;
lld_evt_init = 0x07f08b43;
lld_evt_init_evt = 0x07f08bb9;
lld_evt_elt_insert = 0x07f08bd7;
lld_evt_conhdl2elt = 0x07f08c01;
lld_evt_schedule_next = 0x07f08c1d;
lld_evt_schedule = 0x07f08d19;
lld_evt_prevent_stop = 0x07f08d55;
lld_evt_canceled = 0x07f08d57;
lld_evt_scan_create = 0x07f08d7b;
lld_evt_move_to_master = 0x07f08e6f;
lld_evt_update_create = 0x07f08fef;
lld_evt_ch_map_update_req = 0x07f090f1;
lld_evt_move_to_slave = 0x07f09109;
lld_evt_slave_update = 0x07f09347;
lld_evt_adv_create = 0x07f09405;
lld_evt_end = 0x07f094b9;
lld_evt_rx = 0x07f095c1;
lld_evt_timer_isr = 0x07f095f3;
lld_evt_end_isr = 0x07f095fd;
lld_evt_rx_isr = 0x07f0968b;
lld_sleep_us_2_lpcycles_func = 0x07f09839;
lld_sleep_lpcycles_2_us_func = 0x07f0985f;
lld_sleep_enter = 0x07f09969;
lld_sleep_wakeup = 0x07f099a9;
lld_sleep_wakeup_end = 0x07f099c3;
lld_wlcoex_connection_complete = 0x07f099e9;
lld_wlcoex_remove_connection = 0x07f09a01;
lld_wlcoex_set = 0x07f09a15;
lld_util_get_bd_address = 0x07f09a29;
lld_util_set_bd_address = 0x07f09a49;
lld_util_freq2chnl = 0x07f09a85;
lld_util_get_local_offset = 0x07f09aa7;
lld_util_get_peer_offset = 0x07f09ac1;
lld_util_connection_param_set = 0x07f09add;
llm_wl_clr = 0x07f09b2d;
llm_init = 0x07f09b55;
llm_common_cmd_complete_send = 0x07f09d5b;
llm_ble_ready = 0x07f09d73;
llm_wl_from_rl_restore = 0x07f09d79;
llm_con_req_ind = 0x07f09df9;
llm_resolv_addr = 0x07f0a0ef;
llm_util_rl_wl_update = 0x07f0a131;
llm_alter_conn = 0x07f0a16b;
llm_adv_report_set = 0x07f0a207;
llm_direct_adv_report_set = 0x07f0a295;
llm_encryption_start = 0x07f0a2d7;
llm_resolv_addr_inplace = 0x07f0a37f;
llm_le_adv_report_ind_func = 0x07f0a42b;
llm_con_req_tx_cfm = 0x07f0a93d;
llm_common_cmd_status_send = 0x07f0aa59;
llm_test_mode_start_tx = 0x07f0aa73;
llm_test_mode_start_rx = 0x07f0ab8d;
llm_set_adv_param = 0x07f0abcd;
llm_gen_rand_addr = 0x07f0ad33;
llm_wl_from_rl = 0x07f0adef;
llm_set_adv_en = 0x07f0af1f;
llm_set_adv_data = 0x07f0b225;
llm_set_scan_rsp_data = 0x07f0b2dd;
llm_set_scan_param = 0x07f0b3cd;
llm_set_scan_en = 0x07f0b453;
llm_wl_dev_add = 0x07f0b5a7;
llm_wl_dev_rem = 0x07f0b681;
llm_create_con = 0x07f0b6d5;
llm_encryption_done_func = 0x07f0b9db;
llm_get_chnl_assess_nb_pkt = 0x07f0bcb3;
llm_get_chnl_assess_nb_bad_pkt = 0x07f0bcbb;
llm_get_min_rssi = 0x07f0bcc3;
llm_le_scan_report_ind = 0x07f0bccd;
llm_set_tx_oct_time = 0x07f0bd35;
llm_p256_start_func = 0x07f0bd5f;
llm_create_p256_key_func = 0x07f0bdd5;
llm_p256_req_handler_func = 0x07f0be85;
hci_rd_local_supp_feats_cmd_handler_func = 0x07f0c6b9;
llm_util_bd_addr_in_wl = 0x07f0cf35;
llm_util_check_address_validity = 0x07f0cfab;
llm_util_check_map_validity = 0x07f0cfbb;
llm_util_apply_bd_addr = 0x07f0d001;
llm_util_set_public_addr = 0x07f0d019;
llm_util_check_evt_mask = 0x07f0d027;
llm_util_get_channel_map = 0x07f0d049;
llm_util_get_supp_features = 0x07f0d057;
llm_util_adv_data_update = 0x07f0d063;
llm_util_bl_check = 0x07f0d087;
llm_util_bl_add = 0x07f0d0c9;
llm_util_bl_rem = 0x07f0d11f;
llm_util_rl_check = 0x07f0d16f;
llm_util_rl_add = 0x07f0d1a5;
llm_util_rl_rem = 0x07f0d223;
llm_util_rl_peer_find = 0x07f0d249;
llm_util_rl_peer_resolv = 0x07f0d275;
llm_util_rl_rpa_find = 0x07f0d2c7;
PK_PointMult_func = 0x07f0d2f1;
ea_time_get_slot_rounded = 0x07f0d401;
ea_init = 0x07f0d4cd;
ea_elt_create = 0x07f0d521;
ea_time_get_halfslot_rounded = 0x07f0d53b;
ea_elt_insert = 0x07f0d56b;
ea_elt_remove = 0x07f0d7af;
ea_elt_delete = 0x07f0d837;
ea_interval_create = 0x07f0d851;
ea_interval_insert = 0x07f0d867;
ea_interval_delete = 0x07f0d875;
ea_finetimer_isr = 0x07f0d88f;
ea_sw_isr = 0x07f0d961;
ea_offset_req = 0x07f0d97f;
ea_sleep_check = 0x07f0db3d;
ea_interval_duration_req = 0x07f0db93;
flash_identify = 0x07f0dcb3;
flash_init = 0x07f0dd01;
flash_erase = 0x07f0dd3d;
flash_write = 0x07f0dda1;
flash_read = 0x07f0de05;
uart_init_func = 0x07f0de93;
uart_flow_on_func = 0x07f0def1;
uart_flow_off_func = 0x07f0def9;
uart_finish_transfers_func = 0x07f0df49;
uart_read_func = 0x07f0df61;
uart_write_func = 0x07f0df77;
UART_Handler_func = 0x07f0df99;
uart_set_flow_off_retries_limit = 0x07f0dfeb;
init_delay = 0x07f0e045;
delay_us = 0x07f0e047;
gtl_init_func = 0x07f0e319;
gtl_enter_sleep = 0x07f0e341;
gtl_exit_sleep = 0x07f0e36b;
gtl_send_msg = 0x07f0e373;
gtl_eif_read_start_func = 0x07f0e3fd;
gtl_eif_read_hdr_func = 0x07f0e41d;
gtl_eif_read_payl_func = 0x07f0e43d;
gtl_eif_tx_done_func = 0x07f0e47b;
gtl_eif_rx_done_func = 0x07f0e48b;
gtl_eif_init_func = 0x07f0e5bd;
gtl_eif_write = 0x07f0e5d7;
gtl_eif_start = 0x07f0e5f9;
gtl_eif_stop = 0x07f0e603;
gtl_env_curr_msg_type_set = 0x07f0e61f;
hci_tl_host_cmd_discarded = 0x07f0e8d3;
hci_tl_send = 0x07f0e8f1;
hci_tl_init = 0x07f0e939;
hci_cmd_get_max_param_size = 0x07f0e95d;
hci_cmd_received = 0x07f0e9a7;
hci_acl_tx_data_alloc = 0x07f0eae3;
hci_acl_tx_data_received = 0x07f0eb75;
hci_acl_rx_data_alloc = 0x07f0ebdd;
hci_acl_rx_data_received = 0x07f0ebe9;
hci_evt_received = 0x07f0ec1f;
hci_tl_env_tx_queue_cnt_get = 0x07f0edcb;
hci_util_pack = 0x07f0eee3;
hci_util_unpack = 0x07f0efe3;
hci_look_for_cmd_desc = 0x07f0f4c9;
hci_look_for_evt_desc = 0x07f0f515;
hci_look_for_le_evt_desc = 0x07f0f537;
hci_evt_mask_set = 0x07f0f569;
hci_init = 0x07f0f5b1;
hci_reset = 0x07f0f5cd;
hci_send_2_host = 0x07f0f5e5;
hci_host_cmd_discarded = 0x07f0f6cf;
hci_send_2_controller = 0x07f0f6d7;
h4tl_read_start_func = 0x07f0f7b1;
h4tl_read_hdr_func = 0x07f0f7cf;
h4tl_read_payl_func = 0x07f0f7eb;
h4tl_read_next_out_of_sync_func = 0x07f0f805;
h4tl_out_of_sync_func = 0x07f0f819;
h4tl_out_of_sync_check = 0x07f0f83f;
h4tl_tx_done_func = 0x07f0f897;
h4tl_rx_done_func = 0x07f0f8af;
h4tl_init_func = 0x07f0f9f5;
h4tl_write = 0x07f0fa11;
h4tl_start = 0x07f0fa39;
h4tl_stop = 0x07f0fa41;
h4tl_env_rx_type_set = 0x07f0fa55;
h4tl_env_hdr_set = 0x07f0fa61;
attc_send_att_req = 0x07f0fa9d;
attc_allocate_att_req = 0x07f0fad9;
attc_send_hdl_cfm = 0x07f0fafb;
attc_send_execute = 0x07f0fb11;
attc_send_read_ind = 0x07f0fb2b;
attc_l2cc_pdu_recv_handler_func = 0x07f1046b;
attm_convert_to128 = 0x07f104c5;
attm_uuid_comp = 0x07f104f5;
attm_uuid16_comp = 0x07f1054d;
attm_is_bt16_uuid = 0x07f10559;
attm_is_bt32_uuid = 0x07f1057f;
attmdb_add_service = 0x07f1097f;
attmdb_destroy = 0x07f10a07;
attmdb_get_service = 0x07f10a21;
attmdb_get_attribute = 0x07f10a5f;
attmdb_get_next_att = 0x07f10a93;
attmdb_uuid16_comp = 0x07f10af7;
attmdb_att_set_value = 0x07f10b33;
attmdb_get_max_len = 0x07f10bdf;
attmdb_get_uuid = 0x07f10c45;
attmdb_get_value = 0x07f10d1b;
attmdb_att_set_permission = 0x07f10e6b;
attmdb_att_update_perm = 0x07f10edd;
attmdb_svc_get_permission = 0x07f10f4b;
attmdb_att_get_permission = 0x07f10f69;
attmdb_svc_set_permission = 0x07f1106b;
attmdb_init = 0x07f1108f;
attmdb_get_nb_svc = 0x07f110a5;
attmdb_get_svc_info = 0x07f110b9;
attm_svc_create_db = 0x07f110ed;
attmdb_reserve_handle_range = 0x07f111fb;
atts_clear_read_cache = 0x07f1138d;
atts_send_error = 0x07f11519;
atts_write_signed_cfm = 0x07f11535;
atts_send_event = 0x07f1157b;
atts_clear_prep_data = 0x07f11603;
atts_clear_rsp_data = 0x07f11627;
atts_clear_pending_write_ind_data = 0x07f1167d;
atts_write_rsp_send = 0x07f116a1;
atts_l2cc_pdu_recv_handler_func = 0x07f122b9;
gattc_cleanup = 0x07f1252b;
gattc_init = 0x07f125b5;
gattc_update_state = 0x07f125e7;
gattc_create = 0x07f1260b;
gattc_con_enable = 0x07f1268b;
gattc_get_mtu = 0x07f12691;
gattc_set_mtu = 0x07f1269d;
gattc_get_requester = 0x07f126e3;
gattc_send_complete_evt = 0x07f126ff;
gattc_send_error_evt = 0x07f1275b;
gattc_get_operation = 0x07f12781;
gattc_get_op_seq_num = 0x07f12797;
gattc_get_operation_ptr = 0x07f127ad;
gattc_set_operation_ptr = 0x07f127b9;
gattc_reschedule_operation = 0x07f127c5;
gattc_reallocate_svc = 0x07f12809;
gattm_svc_get_start_hdl = 0x07f13815;
gattm_init = 0x07f1381b;
gattm_init_attr = 0x07f13839;
gattm_create = 0x07f1388d;
gattm_cleanup = 0x07f13895;
gattm_get_max_mtu = 0x07f1389d;
gattm_set_max_mtu = 0x07f138a3;
gattm_get_max_mps = 0x07f138bf;
gattm_set_max_mps = 0x07f138c5;
l2cc_cleanup = 0x07f13b2d;
l2cc_init = 0x07f13b71;
l2cc_create = 0x07f13ba3;
l2cc_update_state = 0x07f13bdb;
hci_acl_data_rx_handler = 0x07f13d97;
l2cm_init = 0x07f14029;
l2cm_create = 0x07f1403d;
l2cm_cleanup = 0x07f14045;
l2cm_set_link_layer_buff_size = 0x07f1404d;
smpc_send_use_enc_block_cmd = 0x07f1405d;
smpc_send_start_enc_cmd = 0x07f14095;
smpc_send_ltk_req_rsp = 0x07f1410f;
smpc_send_pairing_req_ind = 0x07f1416b;
smpc_send_pairing_ind = 0x07f1424f;
smpc_check_pairing_feat = 0x07f1436b;
smpc_launch_rep_att_timer = 0x07f14385;
smpc_check_repeated_attempts = 0x07f143c1;
smpc_check_max_key_size = 0x07f14423;
smpc_check_key_distrib = 0x07f14469;
smpc_xor = 0x07f144b3;
smpc_generate_l = 0x07f144c9;
smpc_generate_ci = 0x07f14517;
smpc_generate_rand = 0x07f1457b;
smpc_generate_e1 = 0x07f145a1;
smpc_generate_cfm = 0x07f1465b;
smpc_generate_stk = 0x07f146d5;
smpc_calc_subkeys = 0x07f1472b;
smpc_clear_timeout_timer = 0x07f147a1;
smpc_pairing_end = 0x07f147cb;
smpc_tkdp_rcp_continue = 0x07f14829;
smpc_tkdp_rcp_start = 0x07f148a1;
smpc_pdu_send = 0x07f148f5;
smpc_tkdp_send_start = 0x07f14993;
smpc_tkdp_send_continue = 0x07f14a1f;
smpc_get_key_sec_prop = 0x07f14a9b;
smpc_is_sec_mode_reached = 0x07f14b67;
smpc_handle_enc_change_evt = 0x07f14ba9;
smpc_pdu_recv_func = 0x07f14c63;
smpc_generate_subkey = 0x07f14ccf;
leftshift_onebit = 0x07f14d03;
padding = 0x07f14d1b;
smpc_generate_subkey_P2 = 0x07f14d3f;
AES_CMAC_block = 0x07f14de3;
smpc_generate_f4 = 0x07f14ea3;
smpc_generate_g2 = 0x07f14fb7;
smpc_generate_f5 = 0x07f15061;
smpc_generate_f5_T = 0x07f15073;
smpc_generate_f5_P2 = 0x07f150e1;
smpc_generate_f6 = 0x07f152b3;
smpm_send_encrypt_req = 0x07f1544d;
smpm_send_gen_rand_nb_req = 0x07f1547b;
smpm_check_addr_type = 0x07f15491;
gapc_update_state = 0x07f154fd;
gapc_get_requester = 0x07f1552d;
gapc_send_complete_evt = 0x07f15549;
gapc_init = 0x07f15673;
gapc_con_create = 0x07f156a5;
gapc_con_create_enh = 0x07f1575f;
gapc_con_cleanup = 0x07f15859;
gapc_send_disconect_ind = 0x07f15869;
gapc_get_conidx = 0x07f1588b;
gapc_get_conhdl = 0x07f158c5;
gapc_get_role = 0x07f158dd;
gapc_get_bdaddr = 0x07f158f9;
gapc_get_csrk = 0x07f15919;
gapc_get_sign_counter = 0x07f15937;
gapc_send_error_evt = 0x07f15955;
gapc_get_operation = 0x07f15977;
gapc_get_operation_ptr = 0x07f1598d;
gapc_set_operation_ptr = 0x07f15999;
gapc_reschedule_operation = 0x07f159a5;
gapc_reschedule_conn_update = 0x07f159d5;
gapc_get_enc_keysize = 0x07f159fb;
gapc_is_sec_set = 0x07f15a13;
gapc_set_enc_keysize = 0x07f15a9f;
gapc_link_encrypted = 0x07f15ab3;
gapc_auth_set = 0x07f15acd;
gapc_svc_chg_ccc_get = 0x07f15aed;
gapc_svc_chg_ccc_set = 0x07f15afd;
gapc_check_lecb_sec_perm = 0x07f15b13;
gapc_search_lecb_channel = 0x07f15b7b;
gapc_lecnx_check_tx = 0x07f15bb5;
gapc_lecnx_check_rx = 0x07f15bfd;
gapc_lecnx_get_field = 0x07f15c41;
gapc_process_op = 0x07f15cb5;
gapc_param_update_sanity = 0x07f15e2f;
gapc_param_cb_con_sanity = 0x07f15e57;
l2cc_pdu_recv_ind_handler_func = 0x07f16323;
gapc_lecb_connect_cfm_handler_func = 0x07f172eb;
gapm_init = 0x07f176c7;
gapm_init_attr = 0x07f17723;
gapm_get_operation = 0x07f1774f;
gapm_get_requester = 0x07f17761;
gapm_reschedule_operation = 0x07f17779;
gapm_send_complete_evt = 0x07f1779b;
gapm_send_error_evt = 0x07f177d1;
gapm_con_create = 0x07f177f1;
gapm_con_enable = 0x07f17875;
gapm_con_cleanup = 0x07f17881;
gapm_get_id_from_task = 0x07f178b1;
gapm_get_task_from_id = 0x07f178f1;
gapm_is_disc_connection = 0x07f1792d;
gapm_adv_sanity = 0x07f18779;
gapm_adv_op_sanity = 0x07f1886d;
gapm_set_adv_mode = 0x07f189f3;
gapm_set_adv_data = 0x07f18a0d;
gapm_execute_adv_op = 0x07f18a9d;
gapm_scan_op_sanity = 0x07f18bc3;
gapm_set_scan_mode = 0x07f18ccb;
gapm_execute_scan_op = 0x07f18ce9;
gapm_connect_op_sanity = 0x07f18da3;
gapm_basic_hci_cmd_send = 0x07f18f23;
gapm_execute_connect_op = 0x07f18f37;
gapm_get_role = 0x07f190d9;
gapm_get_ad_type_flag = 0x07f190e1;
gapm_add_to_filter = 0x07f19107;
gapm_is_filtered = 0x07f19187;
gapm_update_air_op_state = 0x07f191eb;
gapm_get_irk = 0x07f192b3;
gapm_get_bdaddr = 0x07f192b9;
l2cc_pdu_pack_func = 0x07f192d5;
l2cc_detect_dest = 0x07f197d1;
l2cc_handle_invalid_pdu = 0x07f1982d;
l2cc_pdu_unpack_func = 0x07f19943;
l2c_process_sdu_func = 0x07f19c5f;
l2c_send_lecb_message_func = 0x07f19d5f;
smpc_check_param_func = 0x07f19e59;
gapc_hci_handler = 0x07f1aacd;
gapm_hci_handler = 0x07f1b745;
smpc_pairing_start = 0x07f1b7b1;
smpc_pairing_tk_exch = 0x07f1b837;
smpc_pairing_ltk_exch = 0x07f1b8f5;
smpc_pairing_csrk_exch = 0x07f1b949;
smpc_pairing_rsp = 0x07f1b99f;
smpc_pairing_req_handler = 0x07f1ba83;
smpc_security_req_send = 0x07f1babb;
smpc_encrypt_start = 0x07f1bae5;
smpc_encrypt_start_handler = 0x07f1bb0b;
smpc_encrypt_cfm = 0x07f1bb3d;
smpc_sign_command = 0x07f1bb69;
smpc_sign_cont = 0x07f1bc41;
smpc_calc_confirm_cont = 0x07f1bdeb;
smpc_confirm_gen_rand = 0x07f1c32d;
smpc_public_key_exchange_start_func = 0x07f1c3f3;
smpc_dhkey_calc_start = 0x07f1c417;
smpc_sec_authentication_start = 0x07f1c447;
smpc_dhkey_calc_ind_func = 0x07f1c475;
smpm_gen_rand_addr = 0x07f1c4b9;
smpm_resolv_addr = 0x07f1c4d1;
smpm_use_enc_block = 0x07f1c4f3;
smpm_gen_rand_nb = 0x07f1c4fb;
smpm_ecdh_key_create_func = 0x07f1c503;
ke_init = 0x07f1c521;
ke_flush = 0x07f1c553;
ke_sleep_check = 0x07f1c593;
ke_stats_get = 0x07f1c5a5;
ke_event_init = 0x07f1c5c1;
ke_event_callback_set = 0x07f1c5cd;
ke_event_set = 0x07f1c5e1;
ke_event_clear = 0x07f1c60d;
ke_event_get = 0x07f1c639;
ke_event_get_all = 0x07f1c65f;
ke_event_flush = 0x07f1c665;
ke_event_schedule = 0x07f1c66d;
ke_mem_init = 0x07f1c6bd;
ke_mem_is_empty = 0x07f1c709;
ke_check_malloc = 0x07f1c749;
ke_malloc = 0x07f1c7d9;
ke_free = 0x07f1c8cf;
ke_is_free = 0x07f1c9b1;
ke_get_mem_usage = 0x07f1c9c3;
ke_get_max_mem_usage = 0x07f1c9cf;
ke_msg_alloc = 0x07f1c9f5;
ke_msg_send = 0x07f1ca2b;
ke_msg_send_basic = 0x07f1ca57;
ke_msg_forward = 0x07f1ca65;
ke_msg_forward_new_id = 0x07f1ca6f;
ke_msg_free = 0x07f1ca7f;
ke_msg_dest_id_get = 0x07f1ca87;
ke_msg_src_id_get = 0x07f1ca8d;
ke_msg_in_queue = 0x07f1ca93;
ke_queue_extract = 0x07f1caa5;
ke_queue_insert = 0x07f1caf5;
ke_task_init_func = 0x07f1cddf;
ke_task_create = 0x07f1cdf3;
ke_task_delete = 0x07f1ce2b;
ke_state_set = 0x07f1ce57;
ke_state_get = 0x07f1ce81;
ke_msg_discard = 0x07f1ce9f;
ke_msg_save = 0x07f1cea3;
ke_task_msg_flush = 0x07f1cea7;
ke_timer_init_func = 0x07f1d067;
ke_timer_set = 0x07f1d073;
ke_timer_clear = 0x07f1d107;
ke_timer_active = 0x07f1d15d;
ke_timer_sleep_check = 0x07f1d183;
rwble_hl_init = 0x07f1d289;
rwble_hl_reset = 0x07f1d2ab;
rwble_hl_send_message = 0x07f1d2cd;
rwip_check_wakeup_boundary = 0x07f1d2d1;
rwip_init = 0x07f1d2f7;
rwip_reset = 0x07f1d3bb;
rwip_version = 0x07f1d3f3;
rwip_schedule = 0x07f1d3fb;
rwip_prevent_sleep_set = 0x07f1d4a7;
rwip_wakeup = 0x07f1d4c9;
rwip_prevent_sleep_clear = 0x07f1d4df;
rwip_wakeup_end = 0x07f1d501;
rwip_wakeup_delay_set = 0x07f1d51d;
rwip_sleep_enable = 0x07f1d52b;
rwip_ext_wakeup_enable = 0x07f1d531;
rwble_init = 0x07f1d555;
rwble_reset = 0x07f1d5bb;
rwble_version = 0x07f1d5ef;
rwble_send_message = 0x07f1d61b;
YieldToScheduler = 0x07f1d725;
xorshift64star = 0x07f1d72d;
uECC_set_rng = 0x07f1d793;
uECC_get_rng = 0x07f1d799;
uECC_curve_private_key_size = 0x07f1d79f;
uECC_curve_public_key_size = 0x07f1d7af;
uECC_vli_clear = 0x07f1d7b7;
uECC_vli_isZero = 0x07f1d7cd;
uECC_vli_testBit = 0x07f1d7ef;
uECC_vli_numBits = 0x07f1d801;
uECC_vli_set = 0x07f1d83b;
uECC_vli_equal = 0x07f1d879;
uECC_vli_cmp = 0x07f1d89d;
uECC_vli_rshift1 = 0x07f1d8d3;
uECC_vli_square = 0x07f1d8f1;
uECC_vli_modAdd = 0x07f1d8fd;
uECC_vli_modSub = 0x07f1d92b;
uECC_vli_mmod = 0x07f1d94b;
uECC_vli_modMult = 0x07f1da55;
uECC_vli_modMult_fast = 0x07f1da77;
uECC_vli_modSquare = 0x07f1da97;
uECC_vli_modSquare_fast = 0x07f1daa5;
uECC_vli_modInv = 0x07f1dae5;
uECC_secp256r1 = 0x07f1de21;
uECC_vli_nativeToBytes = 0x07f1e41f;
uECC_vli_bytesToNative = 0x07f1e441;
uECC_generate_random_int = 0x07f1e47f;
uECC_make_key = 0x07f1e4e1;
uECC_shared_secret = 0x07f1e55f;
uECC_compress = 0x07f1e61b;
uECC_decompress = 0x07f1e649;
uECC_valid_point = 0x07f1e6b9;
uECC_valid_public_key = 0x07f1e71b;
uECC_compute_public_key = 0x07f1e74f;
uECC_sign = 0x07f1e9d5;
uECC_sign_deterministic = 0x07f1eabb;
uECC_verify = 0x07f1ec29;
uECC_curve_num_words = 0x07f1eed5;
uECC_curve_num_bytes = 0x07f1eedd;
uECC_curve_num_bits = 0x07f1eee5;
uECC_curve_num_n_words = 0x07f1eeed;
uECC_curve_num_n_bytes = 0x07f1eefd;
uECC_curve_num_n_bits = 0x07f1ef0d;
uECC_curve_p = 0x07f1ef15;
uECC_curve_n = 0x07f1ef19;
uECC_curve_G = 0x07f1ef1d;
uECC_curve_b = 0x07f1ef21;
uECC_vli_mod_sqrt = 0x07f1ef25;
uECC_vli_mmod_fast = 0x07f1ef2b;
uECC_point_mult = 0x07f1ef31;
__aeabi_uidiv = 0x07f1f005;
__aeabi_uidivmod = 0x07f1f005;
__aeabi_idiv = 0x07f1f031;
__aeabi_idivmod = 0x07f1f031;
__aeabi_lmul = 0x07f1f059;
_ll_mul = 0x07f1f059;
rand = 0x07f1f0d5;
srand = 0x07f1f0e7;
__aeabi_memcpy = 0x07f1f0f9;
__aeabi_memcpy4 = 0x07f1f0f9;
__aeabi_memcpy8 = 0x07f1f0f9;
__aeabi_memset = 0x07f1f11d;
__aeabi_memset4 = 0x07f1f11d;
__aeabi_memset8 = 0x07f1f11d;
__aeabi_memclr = 0x07f1f12b;
__aeabi_memclr4 = 0x07f1f12b;
__aeabi_memclr8 = 0x07f1f12b;
_memset$wrapper = 0x07f1f12f;
memcmp = 0x07f1f141;
__aeabi_uread4 = 0x07f1f15b;
__rt_uread4 = 0x07f1f15b;
_uread4 = 0x07f1f15b;
__aeabi_uwrite4 = 0x07f1f16f;
__rt_uwrite4 = 0x07f1f16f;
_uwrite4 = 0x07f1f16f;
__aeabi_llsl = 0x07f1f181;
_ll_shift_l = 0x07f1f181;
__ARM_common_switch8 = 0x07f1f1a1;
uart_api = 0x07f1f1bc;
co_sca2ppm = 0x07f1f1cc;
co_null_bdaddr = 0x07f1f1dc;
co_default_bdaddr = 0x07f1f1e2;
llc_state_handler = 0x07f1f468;
llc_default_handler = 0x07f1f538;
llm_debug_private_key = 0x07f1f556;
llm_local_le_states = 0x07f1f588;
llm_state_handler = 0x07f1f770;
llm_default_handler = 0x07f1f7a0;
LLM_AA_CT1 = 0x07f1f7a8;
LLM_AA_CT2 = 0x07f1f7ab;
ecc_p256_G = 0x07f1f7ad;
gtl_default_state = 0x07f1f800;
gtl_default_handler = 0x07f1f808;
hci_cmd_desc_tab_lk_ctrl = 0x07f1f814;
hci_cmd_desc_tab_ctrl_bb = 0x07f1f838;
hci_cmd_desc_tab_info_par = 0x07f1f8b0;
hci_cmd_desc_tab_stat_par = 0x07f1f8e0;
hci_cmd_desc_tab_le = 0x07f1f8ec;
hci_cmd_desc_tab_vs = 0x07f1fb38;
rom_hci_cmd_desc_root_tab = 0x07f1fc7c;
hci_evt_desc_tab = 0x07f1fcac;
hci_evt_le_desc_tab = 0x07f1fcf4;
attc_handlers = 0x07f1fd64;
atts_handlers = 0x07f1fdd4;
gattc_default_state = 0x07f1fe54;
gattc_default_handler = 0x07f1ff34;
gattm_default_state = 0x07f1ff80;
gattm_default_handler = 0x07f1ffd8;
l2cc_default_state = 0x07f1fff0;
l2cc_default_handler = 0x07f20008;
const_Rb = 0x07f20029;
const_Zero = 0x07f20039;
gapc_default_state = 0x07f2005c;
gapc_default_handler = 0x07f201ac;
gapm_default_state = 0x07f20248;
gapm_default_handler = 0x07f20330;
smpc_construct_pdu = 0x07f20454;
dummy = 0x07fc9c00;
ble_wakeup_executed = 0x07fcb900;
rf_in_sleep = 0x07fcb901;
custom_preinit = 0x07fcb904;
custom_postinit = 0x07fcb908;
custom_appinit = 0x07fcb90c;
custom_preloop = 0x07fcb910;
custom_preschedule = 0x07fcb914;
custom_postschedule = 0x07fcb918;
custom_postschedule_async = 0x07fcb91c;
custom_presleepcheck = 0x07fcb920;
custom_appsleepset = 0x07fcb924;
custom_postsleepcheck = 0x07fcb928;
custom_presleepenter = 0x07fcb92c;
custom_postsleepexit = 0x07fcb930;
custom_prewakeup = 0x07fcb934;
custom_postwakeup = 0x07fcb938;
custom_preidlecheck = 0x07fcb93c;
custom_pti_set = 0x07fcb940;
REG_BLE_EM_TX_BUFFER_SIZE = 0x07fcb944;
REG_BLE_EM_RX_BUFFER_SIZE = 0x07fcb948;
_ble_base = 0x07fcb94c;
gap_cfg_user = 0x07fcb950;
rom_func_addr_table = 0x07fcb954;
rom_cfg_table = 0x07fcb958;
BLE_TX_DESC_DATA_USER = 0x07fcb95c;
BLE_TX_DESC_CNTL_USER = 0x07fcb960;
LLM_LE_ADV_DUMMY_IDX = 0x07fcb964;
LLM_LE_SCAN_CON_REQ_ADV_DIR_IDX = 0x07fcb968;
LLM_LE_SCAN_RSP_IDX = 0x07fcb96c;
LLM_LE_ADV_IDX = 0x07fcb970;
length_exchange_needed = 0x07fcb974;
enh_con_cmp_cnt = 0x07fcb978;
rx_pkt_cnt = 0x07fcb980;
rx_pkt_cnt_bad = 0x07fcb984;
rx_pkt_cnt_bad_adv = 0x07fcb988;
rx_pkt_cnt_bad_scn = 0x07fcb98c;
rx_pkt_cnt_bad_oth = 0x07fcb990;
rx_pkt_cnt_bad_wo_sync_err = 0x07fcb994;
rx_pkt_cnt_bad_con = 0x07fcb998;
connect_req_cnt = 0x07fcb99c;
last_status = 0x07fcb9a0;
llc_state = 0x07fcb9a4;
lld_wlcoex_enable = 0x07fcb9ac;
ble_duplicate_filter_max = 0x07fcb9b0;
ble_duplicate_filter_found = 0x07fcb9b1;
alter_conn_adv_all_cnt = 0x07fcb9b4;
alter_conn_adv_dir_cnt = 0x07fcb9b8;
alter_conn_adv_cnt = 0x07fcb9bc;
create_conn_cnt = 0x07fcb9c0;
alter_conn_cnt = 0x07fcb9c4;
alter_conn_restart_cnt = 0x07fcb9c8;
alter_conn_peer_addr = 0x07fcb9cc;
alter_conn_local_addr = 0x07fcb9d2;
set_adv_data_discard_old = 0x07fcb9d8;
llm_resolving_list_max = 0x07fcb9d9;
llm_local_le_feats = 0x07fcb9da;
llm_bt_env = 0x07fcb9e2;
init_tx_cnt_cntl_cnt1 = 0x07fcb9ec;
init_tx_cnt_cntl_cnt = 0x07fcb9f0;
tx_cnt_cntl_cnt = 0x07fcb9f4;
llm_state = 0x07fcb9f8;
delay_us_cnt = 0x07fcb9fa;
gtl_state = 0x07fcb9fc;
use_h4tl = 0x07fcb9fd;
hci_cmd_desc_root_tab = 0x07fcba00;
gattc_state = 0x07fcba30;
gattm_state = 0x07fcba33;
l2cc_state = 0x07fcba34;
l2cm_env = 0x07fcba38;
gapc_state = 0x07fcba3e;
gapm_state = 0x07fcba41;
whitelist_fix = 0x07fcba42;
ecdh_key_creation_in_progress = 0x07fcba44;
ke_free_bad = 0x07fcba48;
DISABLE_KE_TASK_ALTERNATIVE_SAVED_QUEUE = 0x07fcba4c;
rwip_env = 0x07fcba54;
custom_msg_handlers = 0x07fcba60;
ble_reg_save = 0x07fcba64;
sleep_env = 0x07fcbab4;
uart_env = 0x07fcbab8;
ke_mem_heaps_used = 0x07fcbadc;
co_buf_env = 0x07fcbae0;
llc_env = 0x07fcbb78;
lld_evt_env = 0x07fcbb84;
llm_le_env = 0x07fcbbb0;
llm_local_cmds = 0x07fcbcb0;
gtl_env = 0x07fcbd30;
hci_env = 0x07fcbd78;
gattc_env = 0x07fcbda0;
gattm_env = 0x07fcbdac;
l2cc_env = 0x07fcbdd0;
ecdh_key = 0x07fcbddc;
gapc_env = 0x07fcbe3c;
gapm_env = 0x07fcbe48;
ke_env = 0x07fcbe74;
rwip_rf = 0x07fcbf58;

/* 
 * Added by SDK6 
 */

lld_sleep_env = 0x07fcb9a8;
h4tl_env = 0x07fcbd88;
blank_otp_bdaddr = 0x07f239e4;

/* 
 * SDK6 symbols in DA14531 ROM
 */
 
/* arch_console.c (controlled by __EXCLUDE_ROM_ARCH_CONSOLE__) */
arch_printf_flush = 0x07f20be5;
arch_vprintf = 0x07f20c9d;
arch_printf = 0x07f20cfd;
arch_puts = 0x07f20d11;
arch_printf_process = 0x07f20d21;

/* nvds.c (controlled by __EXCLUDE_ROM_NVDS__) */
nvds_get_func = 0x07f20dcd;
nvds_init_func = 0x07f20ea9;
nvds_del_func = 0x07f20ead;
nvds_put_func = 0x07f20eb1;

/* chacha20.c (controlled by __EXCLUDE_ROM_CHACHA20__) */
csprng_seed = 0x07f20f49;
csprng_get_next_uint32 = 0x07f20f79;

/* TRNG implementation in ROM */
trng_acquire = 0x07f21021;

/* prf.c (controlled by __EXCLUDE_ROM_PRF__) */
prf_add_profile_func = 0x07f210d1;
prf_cleanup_func = 0x07f211b1;
prf_env_get = 0x07f211f1;
prf_src_task_get = 0x07f2121d;
prf_dst_task_get = 0x07f2122d;
prf_get_id_from_task_func = 0x07f21241;
prf_get_task_from_id_func = 0x07f21279;
prf_reset_func = 0x07f212b1;
prf_itf_get = 0x07f212fd;

/* prf_utils.c (controlled by __EXCLUDE_ROM_PRF_UTILS__) */
prf_pack_char_pres_fmt = 0x07f21321;
prf_pack_date_time = 0x07f2133f;
prf_unpack_date_time = 0x07f2135f;

/* diss.c (controlled by __EXCLUDE_ROM_DISS__) */
diss_compute_cfg_flag = 0x07f21381;
diss_handle_to_value = 0x07f21453;
diss_value_to_handle = 0x07f21483;
diss_check_val_len = 0x07f214b7;
diss_prf_itf_get = 0x07f214ed;

/* bass.c (controlled by __EXCLUDE_ROM_BASS__) */
bass_get_att_handle = 0x07f2184b;
bass_get_att_idx = 0x07f21901;
bass_exe_operation = 0x07f2196b;
bass_prf_itf_get = 0x07f21a6d;

/* suotar.c (controlled by __EXCLUDE_ROM_SUOTAR__) */
suotar_prf_itf_get = 0x07f22059;

/* custom_common.c (controlled by __EXCLUDE_ROM_CUSTOM_COMMON__) */
check_client_char_cfg = 0x07f22355;
get_value_handle = 0x07f2237f;
get_cfg_handle = 0x07f223cb;
custs1_get_att_handle = 0x07f2242d;
custs1_get_att_idx = 0x07f22449;

/* custs1.c (controlled by __EXCLUDE_ROM_CUSTS1__) */
custs1_prf_itf_get = 0x07f22621;

/* custs1_task.c (controlled by __EXCLUDE_ROM_CUSTS1__) */
custs1_init_ccc_values = 0x07f226d3;
custs1_set_ccc_value = 0x07f2270b;
gattc_cmp_evt_handler = 0x07f22823;
custs1_val_set_req_handler = 0x07f22837;
custs1_val_ntf_req_handler = 0x07f22857;
custs1_val_ind_req_handler = 0x07f228b3;
custs1_att_info_rsp_handler = 0x07f2290f;
gattc_read_req_ind_handler = 0x07f2294b;
gattc_att_info_req_ind_handler = 0x07f22b57;
custs1_value_req_rsp_handler = 0x07f22b99;

/* attm_db_128.c (controlled by __EXCLUDE_ROM_ATTM_DB_128__) */
attm_svc_create_db_128 = 0x07f22c19;

/* app_entry_point.c (__EXCLUDE_ROM_APP_TASK__) */
app_entry_point_handler = 0x07f232a9;
app_std_process_event = 0x07f232f1;

/* app_utils.c - (controlled by __EXCLUDE_ROM_APP_UTILS__) */
app_get_address_type_ROM = 0x07f23335;
app_fill_random_byte_array_ROM = 0x07f23361;

/* ARM library stuff */
__aeabi_ldivmod = 0x07f233f3;
__aeabi_llsr = 0x07f2343f;
_ll_ushift_r = 0x07f2343f;
__aeabi_uldivmod = 0x07f23461;

/* app.c (controlled by __EXCLUDE_ROM_APP_TASK__) */
app_db_init_start = 0x07f234c1;
app_db_init = 0x07f234dd;
app_easy_gap_confirm = 0x07f234e9;
append_device_name = 0x07f23515;
app_easy_gap_update_adv_data = 0x07f23539;
app_easy_gap_disconnect = 0x07f23581;
app_easy_gap_advertise_stop = 0x07f235bd;
active_conidx_to_conhdl = 0x07f235d9;
active_conhdl_to_conidx = 0x07f23605;
app_timer_set = 0x07f23641;
app_easy_gap_set_data_packet_length = 0x07f2365d;
get_user_prf_srv_perm = 0x07f23699;
app_set_prf_srv_perm = 0x07f236c1;
prf_init_srv_perm = 0x07f236f1;
app_gattc_svc_changed_cmd_send = 0x07f23715;

/* (controlled by __EXCLUDE_ROM_APP_TASK__) */
app_default_handler = 0x07f23f58;

/* (controlled by __EXCLUDE_ROM_GAP_CFG_DATA__) */
gap_cfg_user_var_struct = 0x07f23f60;

/* app_task.c handlers in ROM visible to SDK6 */
gapm_adv_report_ind_handler_ROM = 0x07f23085;
gapc_security_ind_handler_ROM = 0x07f2309f;
gapc_set_dev_info_req_ind_handler_ROM = 0x07f23185;
gapm_profile_added_ind_handler_ROM = 0x07f231c7;
gapc_param_update_req_ind_handler_ROM = 0x07f231f9;
gapc_le_pkt_size_ind_handler_ROM = 0x07f23239;
gattc_svc_changed_cfg_ind_handler_ROM = 0x07f23253;
gapc_peer_features_ind_handler_ROM = 0x07f2326f;

/* RW _rand_state variable in stdlib/rand.c (microlib) */
_rand_state_ROM_DATA = 0x07fcba5c;

/* symbols used by patch library */
l2cc_signaling_pkt_format = 0x07f20340;
l2cc_security_pkt_format = 0x07f2039c;
l2cc_attribute_pkt_format = 0x07f203d8;
l2cc_connor_pkt_format = 0x07f20338;
