/*! @file iid_irng.h

    \copyright Copyright 2018 Intrinsic ID B.V. All rights reserved.\n
    This text contains proprietary, confidential information of Intrinsic ID B.V.,
    and may be used, copied, distributed and/or published only pursuant to the
    terms of a valid license agreement with Intrinsic ID B.V.\n
    This copyright notice must be retained as part of this text at all times.

    Copyright (C) 2019 Modified by Dialog Semiconductor
*/

#ifndef _IID_IRNG_H_
#define _IID_IRNG_H_

/// @name Public Macros
///@{
/** Size of a Block in bytes */
#define IRNG_BLOCK_SIZE_BYTES           (16)
/** Number of bytes for the seed randomly generated */
#define IRNG_RANDOM_SEED_SIZE_BYTES     (16)
/** Number of minimum required SRAM PUF blocks */
#define IRNG_MINIMUM_SRAM_PUF_BLOCKS    (28)
///@}

#endif /* _IID_IRNG_H_ */
