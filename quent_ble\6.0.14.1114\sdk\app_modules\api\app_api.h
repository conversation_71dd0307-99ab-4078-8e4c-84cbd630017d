/**
 ****************************************************************************************
 *
 * @file app_api.h
 *
 * @brief app - project api header file.
 *
 * Copyright (C) 2012-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */

#ifndef _APP_API_H_
#define _APP_API_H_

/*
 * INCLUDE FILES
 ****************************************************************************************
 */

#include "rwble_config.h"
#include "gapc_task.h"
#include "gapm_task.h"
#include "gattm_task.h"
#include "gattc_task.h"
#include "app.h"
#include "app_task.h"
#include "app_user_config.h"
#include "app_entry_point.h"
#include "app_default_handlers.h"
#include "app_callback.h"
#include "app_easy_gap.h"
#include "app_easy_msg_utils.h"
#include "app_easy_security.h"
#include "app_easy_timer.h"
#include "app_mid.h"
#include "app_msg_utils.h"
#include "app_utils.h"
#include "arch_api.h"
#include "arch_wdg.h"

#endif // _APP_API_H_
