/*
 * AwtQueue.h
 *
 *  Created on: 05-Oct-2023
 *      Author: admin
 */

#ifndef APPLICATION_USER_AWT_QUEUE_AWTQUEUE_H_
#define APPLICATION_USER_AWT_QUEUE_AWTQUEUE_H_

#include "Comm_Manager.h"

void InitQueue(void);
int enqueueIndTx(pe_trigger* item);
int enqueueCmdTx(pe_trigger* item);
int dequeueCmdTx(pe_trigger* item);
int dequeueIndTx(pe_trigger* item);
int isIndTxFull(void);
int isCmdTxFull(void);
int isCmdTxEmpty(void);
int isIndTxEmpty(void);
int enqueueIndTxToFront(pe_trigger* item);
int enqueueCmdTxToFront(pe_trigger* item);

#endif /* APPLICATION_USER_AWT_QUEUE_AWTQUEUE_H_ */
