/*
 * PrimitiveManager.c
 *
 *  Created on: 09-Sep-2023
 *      Author: admin
 */
#include"Comm_Manager.h"
#include"PrimitiveQueue.h"
#include"UpperLayerInterface.h"
#include"Comm_Debug.h"


#if PRIMITIVE_MANAGER_DEBUG
#define DEBUG_PRINTS
#else
#undef DEBUG_PRINTS
#endif

static pe_trigger iTrigger       = {comm_NONE, p_invalid, 0, 0, 0};     // current indication trigger
static pe_trigger crTrigger      = {comm_NONE, p_invalid, 0, 0, 0};     // current command response trigger
static pe_trigger irtTrigger     = {comm_NONE, p_invalid, 0, 0, 0};     // indication real time triggers
static pe_trigger crtTrigger     = {comm_NONE, p_invalid, 0, 0, 0};     // command response real time triggers
static pe_trigger crExecTrigger  = {comm_NONE, p_invalid, 0, 0, 0};     // non realtime execute triggers from Commands
static pe_trigger temp           = {comm_NONE, p_invalid, 0, 0, 0};     // local storage of triggers
static enum _ptrigger currentEvent = p_ind_change_state;
static int usageCount   = 0;
static int cr_master    = 0;
static int i_master     = 0;
static int pendTimeout  = 0;

int32_t resetPrimitiveManagerCR(void);
int32_t resetPrimitiveManagerInd(void);
void CopyTrigger(pe_trigger* dtrigger,pe_trigger* strigger);

static int cmd_tx_event();
static int cmd_f_event();
static int cmd_s_event();
static int resp_r_event();
static int cr_ack_s_event();
static int cr_nack_s_event();
static int cmd_r_event();
static int rsp_s_event();
static int rsp_f_event();
static int ind_tx_event();
static int ind_r_event();
static int ind_s_event();
static int ind_f_event();
static int ind_ack_s_event();
static int ind_nack_s_event();
static int cr_exec_s_event();
static int cr_exec_f_event();
static int ind_exec_s_event();
static int ind_exec_f_event();
static int cr_transp_f_event();
static int ind_transp_f_event();
static int bufferxfer_s_event();
static int bufferxfer_f_event();
static int bufferxfer_exec_f_event();
static int bufferxfer_exec_s_event();
static int handle_cr_change_state_event(void);
static int handle_ind_change_state_event(void);
static int bufferxfer_event(void);
static int bufferxfer_rx_event();
static int handle_cr_timeout_event();
static int handle_ind_timeout_event();
static int handle_invalid_event();


typedef int (*_ptrigger_function)();

static const _ptrigger_function ptrigger_function[]={
		cmd_tx_event,								// command tx event
		cmd_f_event,								// Command fail event
		cmd_s_event,								// command sucess event
		resp_r_event,								// response received event
		cr_ack_s_event, 							// command response ack sent event
		cr_nack_s_event,							// command response nack sent event
		cr_exec_s_event,							// command/response execute success event
		cr_exec_f_event,							// command/response execute fail event
		cmd_r_event,								//command received event
		rsp_s_event,								// response sent event
		rsp_f_event,								// response fail event
		cr_transp_f_event, 							// command/response transport fail event
		handle_cr_change_state_event,   			// command/response change state event
		handle_cr_timeout_event,							// command/response timeout event
		ind_tx_event,								// indication tx event
		ind_r_event,								// indication rx event
		ind_s_event,								// indication sent event
		ind_f_event,								// indication fail event
		ind_ack_s_event,  							// indication ack sent event
		ind_nack_s_event, 							// indication nack sent event
		ind_exec_s_event, 							// indication execute success event
		ind_exec_f_event, 							// indication execute fail event
		bufferxfer_event,
		bufferxfer_rx_event,
		bufferxfer_s_event,							// Bulk success event
		bufferxfer_f_event,
		bufferxfer_exec_s_event,							// bufferxfer execute success
		bufferxfer_exec_f_event,							// bufferxfer execute fail
		ind_transp_f_event,							// indication transport fail event
		handle_ind_change_state_event,  			// indication change state event
		handle_ind_timeout_event,							// indication timeout event
		handle_invalid_event 					//  invalid event
};



enum p_states
{
	p_cr_idle,       //idle
	p_csend,      // command send
	p_csuccess,   // command send success
	p_rrecv,      // response received
	p_rexec,      // response executed
	p_crecv,      // command receive
	p_cexec,      // command execute
	p_rsend,      // response sent
	p_cr_tout, // cr timeout
	p_cr_msgfail,    // command/response message fail
	p_cr_transpfail,  // command/response transport fail
	p_cr_invalidstate, // command/reponse invalid
	p_ind_idle,
	p_isend,      // indication send
	p_irecv,      // indication receive
	p_iexec,      // indication executed
	p_bufferxfer,			// Send Bulk transfer
	p_bufferxfer_rx,
	p_exec_bufferxfer,
	p_ind_touts,   // indication timeout
	p_ind_msgfail,    // message fail - nack
	p_ind_transpfail,  // transport fail
	p_ind_invalidstate
};


static enum p_states curState_CR = p_cr_idle;
static enum p_states prevState_CR = p_cr_idle;

static enum p_states curState_I = p_ind_idle;
static enum p_states prevState_I = p_ind_idle;

static int p_cr_changeState(enum p_states state);
static int p_ind_changeState(enum p_states state);

static int IsCR(enum _ptrigger _trigger);
static int IsInd(enum _ptrigger _trigger);

static int32_t cr_exec_status  = 0;
static int32_t ind_exec_status = 0;

static int handle_p_cr_idle();
static int handle_p_csend();
static int handle_p_csuccess();
static int handle_p_rrecv();
static int handle_p_rexec();
static int handle_p_crecv();
static int handle_p_cexec();
static int handle_p_rsend();
static int handle_p_isend();
static int handle_p_irecv();
static int handle_p_iexec();
static int handle_p_cr_msgfail();
static int handle_p_bufferxfer();
static int handle_p_bufferxfer_rx(void);
static int handle_p_exec_bufferxfer(void);
static int handle_p_cr_transpfail();

static int handle_p_ind_msgfail();
static int handle_p_ind_transpfail();
static int handle_p_ind_idle();
static int handle_p_ind_invalid();
static int handle_p_cr_invalid();

static int handle_p_cr_timeout();
static int handle_p_ind_timeout();



typedef int (*_pstate_function)();

static const _pstate_function pstate_function[]={
		handle_p_cr_idle,
		handle_p_csend,
		handle_p_csuccess,
		handle_p_rrecv,
		handle_p_rexec,
		handle_p_crecv,
		handle_p_cexec,
		handle_p_rsend,
		handle_p_cr_timeout,
		handle_p_cr_msgfail,
		handle_p_cr_transpfail,
		handle_p_cr_invalid,
		// indications
		handle_p_ind_idle,
		handle_p_isend,
		handle_p_irecv,
		handle_p_iexec,
		handle_p_bufferxfer,
		handle_p_bufferxfer_rx,
		handle_p_exec_bufferxfer,
		handle_p_ind_timeout,
		handle_p_ind_msgfail,
		handle_p_ind_transpfail,
		handle_p_ind_invalid
};

static int cmd_tx_event()
{
	if(curState_CR == p_cr_idle)
	{
		return p_cr_changeState(p_csend);
	}
	return -1;
}

static int cmd_f_event()
{
	if(curState_CR == p_csend)
	{
	return p_cr_changeState(p_cr_msgfail);
	}
	return -1;
}

static int cmd_s_event()
{
	if(curState_CR == p_csend)
	{
	return p_cr_changeState(p_csuccess);
	}
	return -1;

}

static int resp_r_event()
{
	if(curState_CR == p_csuccess)
	{
		return p_cr_changeState(p_rrecv);
	}
	else
	{
		SetTransportTrigger(t_nack);
		return(STATE_WAIT);
	}
	return -1;
}

static int cr_ack_s_event()
{
	if(curState_CR == p_rexec )
	{
		return p_cr_changeState(p_cr_idle);
	}
	else if (curState_CR == p_crecv)  // command recv state is not handing ack sent event
	{
		return p_cr_changeState(p_cexec);
	}
	return -1;
}

static int cr_nack_s_event()
{

	if(curState_CR == p_rexec )
	{
		return p_cr_changeState(p_cr_msgfail);
	}
	else if (curState_CR == p_crecv)  // command recv state is not handing ack sent event
	{
		return p_cr_changeState(p_cr_idle);
	}
	return -1;
}

static int cmd_r_event()
{
	if(curState_CR == p_cr_idle)
	{
		return p_cr_changeState(p_crecv);
	}
	else
	{
		if(crTrigger.sub_id == temp.sub_id)
		{
			resetPrimitiveManagerCR();
			memcpy(&crTrigger,&temp,sizeof(pe_trigger));
			return p_cr_changeState(p_crecv);
		}
		else
		SetTransportTrigger(t_nack);
		return(STATE_WAIT);
	}
	return -1;
}

static int rsp_s_event()
{
	if(curState_CR == p_rsend)
	{
		return p_cr_changeState(p_cr_idle);
	}
	return -1;
}

static int rsp_f_event()
{
	if(curState_CR == p_rsend)
	{
		return p_cr_changeState(p_cr_idle);
	}
	return -1;
}

static int ind_tx_event()
{
	if(curState_I == p_ind_idle)
	{
		return p_ind_changeState(p_isend);
	}
	return -1;
}

static int ind_r_event()
{
	if(curState_I == p_ind_idle)
	{
		return p_ind_changeState(p_irecv);
	}
	else
	{
		SetTransportTrigger(t_nack);
		return(STATE_WAIT);
	}
	return -1;
}

static int ind_s_event()
{
	if(curState_I == p_isend)
	{
		return p_ind_changeState(p_ind_idle);
	}
	return -1;
}

static int ind_f_event()
{
	if(curState_I == p_isend)
	{
		return p_ind_changeState(p_ind_msgfail);
	}
	return -1;
}

static int ind_ack_s_event()
{
	if(curState_I == p_iexec || curState_I == p_exec_bufferxfer)
	{
		return p_ind_changeState(p_ind_idle);
	}
	return -1;
}

static int ind_nack_s_event()
{
	if(curState_I == p_iexec || curState_I == p_exec_bufferxfer)
	{
		return p_ind_changeState(p_ind_idle);
	}
	return -1;
}

static int cr_exec_s_event()
{
	cr_exec_status = 1;

	if(curState_CR == p_rexec)
	{
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	else if( curState_CR == p_cexec)
	{
		return p_cr_changeState(p_rsend);
	}
	return -1;
}

static int cr_exec_f_event()
{
	cr_exec_status = 0;

	if(curState_CR == p_rexec)
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	else if(curState_CR == p_cexec)
	{
		return p_cr_changeState(p_rsend);
	}
	return -1;
}
static int ind_exec_s_event()
{
	ind_exec_status = 1;

	if(curState_I == p_iexec )
	{
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	return -1;
}
static int ind_exec_f_event()
{
	ind_exec_status = 0;

	if(curState_I == p_iexec)
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	return -1;
}

static int ind_transp_f_event()
{

	return p_ind_changeState(p_ind_transpfail);
}

static int bufferxfer_event(void)
{
	if(curState_I == p_bufferxfer_rx || p_ind_idle)
	{
		return p_ind_changeState(p_isend);
	}
	return -1;
}

static int bufferxfer_rx_event()
{
	if(curState_I == p_bufferxfer_rx || p_ind_idle)
	{
		return p_ind_changeState(p_bufferxfer_rx);
	}
	return -1;
}

static int bufferxfer_s_event()
{
	if(curState_I == p_bufferxfer)
	{
		return p_ind_changeState(p_ind_idle);
	}
	return -1;
}

static int bufferxfer_f_event()
{
	if(curState_I == p_bufferxfer)
	{
		return p_ind_changeState(p_ind_msgfail);
	}
	return -1;
}

static int bufferxfer_exec_s_event()
{
	ind_exec_status = 1;

	if(curState_I == p_exec_bufferxfer)
	{
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	return -1;
}

static int bufferxfer_exec_f_event()
{
	ind_exec_status = 0;

	if(curState_I == p_exec_bufferxfer)
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	return -1;
}

static int handle_ind_change_state_event(void)
{
	int ret;
	ret = pstate_function[curState_I]();
	return ret;
}

static int handle_ind_timeout_event()
{
	return p_ind_changeState(p_ind_touts);
}

static int handle_invalid_event()
{
	return -1;
}

static int cr_transp_f_event()
{
	return p_cr_changeState(p_cr_transpfail);
}

static int handle_cr_change_state_event(void)
{
	int ret;
	ret = pstate_function[curState_CR]();
	return ret;
}

static int handle_cr_timeout_event()
{
	return p_cr_changeState(p_cr_tout);
}

static int handle_p_cr_timeout()
{
	if(crTrigger.retry > 0)
	{
		crTrigger.trigger = cmd_tx;
		crTrigger.payload = 0;
		crTrigger.len = 0;
		crTrigger.retry--;
		triggerFromPrimitive(&crTrigger);
		crTrigger.trigger = p_invalid;

		if(curState_I == p_ind_idle)
		{
			SetTransportTrigger(t_timeout);
		}
		else
		{
			pendTimeout = 1;
		}
	}
	else if (crTrigger.retry == 0)
	{
		// retry failed
		return p_cr_changeState(p_cr_msgfail);
	}
	return p_cr_changeState(p_cr_idle);
}

static int handle_p_ind_timeout()
{
	if(iTrigger.retry > 0)
	{
		iTrigger.retry--;
		triggerFromPrimitive(&iTrigger);
		iTrigger.trigger = p_invalid;
		SetTransportTrigger(t_timeout);
	}
	else if (iTrigger.retry == 0)
	{
		// retry failed
		return p_ind_changeState(p_ind_msgfail);
	}
	return p_ind_changeState(p_ind_idle);
}

static int handle_p_cr_idle()
{
	COM_PRINTI("In handle_p_cr_idle usage %d, cr_master[%d] i_master[%d]\r\n", usageCount, cr_master, i_master);
	if(usageCount > 0)
	{
		--usageCount;
	}

	if(cr_master)
	{
		cr_master = 0;
		if(prevState_CR != p_cr_tout)
		{
			COM_PRINTI("CRTimeout Timer Stopped\r\n");
			stopCRTimer();

			if(usageCount == 0 && isCmdTxEmpty() && isIndTxEmpty() && IsComMessageEmpty())
			{
				COM_PRINTI("p_cr_idle closing transport\r\n");
				SetTransportTrigger(t_close_event);
				Comm_Can_Sleep();

			}
		}
	}
	crTrigger.payload = 0;
	crTrigger.sub_id  = comm_NONE;
	crTrigger.trigger = p_invalid;
	crTrigger.len     = 0;
	crTrigger.retry   = 0;
	return(STATE_WAIT);
}

static int handle_p_ind_idle()
{
	COM_PRINTI("In handle_p_ind_idle usage %d cr_master[%d] i_master[%d]\r\n", usageCount, cr_master, i_master);
	if(usageCount > 0)
	{
		usageCount--;
	}

	if(i_master)
	{
		i_master = 0;

		if(prevState_I != p_ind_touts)
		{
			stopITimer();
			if(usageCount == 0 && isIndTxEmpty() && isCmdTxEmpty() && IsComMessageEmpty())
			{
				COM_PRINTI("_p_ind_idle closing transport\r\n");
				SetTransportTrigger(t_close_event);
				Comm_Can_Sleep();
			}
		}
	}

	if(pendTimeout)
	{
		pendTimeout = 0;
		SetTransportTrigger(t_timeout);

	}

	iTrigger.payload = 0;
	iTrigger.sub_id  = comm_NONE;
	iTrigger.trigger = p_invalid;
	iTrigger.len	 = 0;

	return(STATE_WAIT);
}

static int handle_p_csend()
{
	usageCount++;
	cr_master = 1;
	startCRTimer(CRTIME);
	dequeueCmdTx(&crTrigger);
	COM_PRINTI("In handle_p_csend retry %d usage %d cr_master %d i_master %d\n",crTrigger.retry,usageCount,cr_master,i_master);
	WriteCmd(GetTManagerContext(),crTrigger.sub_id);
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}


static int handle_p_csuccess()
{
	COM_PRINTI("In handle_p_csuccess\n");
	return(STATE_WAIT);
}

static int handle_p_rrecv()
{
	COM_PRINTI("In handle_p_rrecv\n");
	CopyTrigger(&crTrigger,&temp);
	return p_cr_changeState(p_rexec);
}

static int handle_p_rexec()
{
	COM_PRINTI("In handle_p_rexec\n");
	switch(Execute_Response_API(crTrigger.sub_id, crTrigger.payload, crTrigger.len))
	{
	case UL_S:break;
	case UL_F:
	{
		SetTransportTrigger(t_nack);
	}
	break;
	case UL_BUSY:
	case UL_C:
	{
		SetTransportTrigger(t_ack);
	}
	break;
	default:break;
	}
	return STATE_WAIT;
}

static int handle_p_crecv()
{
	usageCount++;
	COM_PRINTI("In handle_p_crecv usage %d cr_master %d i_master %d\n",usageCount,cr_master,i_master);
	CopyTrigger(&crTrigger,&temp);
	if(CmdIsServicable(crTrigger.sub_id))
	{
		COM_PRINTI("In handle_p_crecv sending t_ack\n");
		SetTransportTrigger(t_ack);
		return(STATE_WAIT);
	}
	else
	{
		COM_PRINTI("In handle_p_crecv sending t_nack\n");
		SetTransportTrigger(t_nack);
		return p_cr_changeState(p_cr_idle);
	}
	return STATE_WAIT;
}

static int handle_p_cexec()
{
	COM_PRINTI("In handle_p_cexec\n");
	switch(Execute_Command_API(crTrigger.sub_id))
	{
	case UL_S:
	{
		COM_PRINTI("In handle_p_cexec ->UL_S\n");
	}break;
	case UL_F:
	{
		COM_PRINTI("In handle_p_cexec ->UL_F [t_nack] changing p_cr_msgfail\n");
		cr_exec_status = 0;
		return p_cr_changeState(p_rsend);
	}
	case UL_C:
	{
		COM_PRINTI("In handle_p_cexec ->UL_C changing to p_rsend \n");
		cr_exec_status = 1;
		return p_cr_changeState(p_rsend);
	}
	case UL_BUSY:
		cr_exec_status = ERR_VITAL_BUSY;
		return p_cr_changeState(p_rsend);
	default: break;
	}
	return STATE_WAIT;
}

static int handle_p_rsend()
{
	COM_PRINTI("In handle_p_rsend\n");
	Response* resp = GetTManagerContext();
	resp->ID = RESPONSE;
	resp->SUB_ID = crTrigger.sub_id;
	//TODO : Modify MakePacket to include origin setting
	MakePacket(resp, crTrigger.payload, crTrigger.len);
	_platform_upd_requester(resp, cr_exec_status);
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}

static int handle_p_isend()
{
	i_master = 1;
	usageCount++;
	dequeueIndTx(&iTrigger);
	COM_PRINTI("In handle_p_isend usage %d cr_master %d i_master %d\n",usageCount,cr_master,i_master);

	if(iTrigger.trigger == buff_tx)
			return p_ind_changeState(p_bufferxfer);

	startITimer(ITIME);
	Indication* ind = GetTManagerContext();
	ind->ID = INDICATION;
	ind->SUB_ID = iTrigger.sub_id;
	//TODO : Modify MakePacket to include origin setting
	MakePacket(ind, iTrigger.payload, iTrigger.len);
	if(ind->SUB_ID >=comm_wHR && ind->SUB_ID <=comm_wECG)
	{
		ind->Data[4] = 0x1F&Watch;
	}
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}

static int handle_p_irecv()
{
	usageCount++;
	CopyTrigger(&iTrigger,&temp);
	COM_PRINTI("In handle_p_irecv usage %d cr_master %d i_master %d\n",usageCount,cr_master,i_master);
	if(IndIsServicable(iTrigger.sub_id))
	{
		COM_PRINTI("In handle_p_irecv -> p_iexec\n");
		return p_ind_changeState(p_iexec);
	}
	else
	{
		COM_PRINTI("In handle_p_irecv ->p_ind_idle sending t_nack\n");
		SetTransportTrigger(t_nack);
		return p_ind_changeState(p_ind_idle);
	}
	return STATE_WAIT;
}

static int handle_p_iexec()
{
	COM_PRINTI("In handle_p_iexec\n");
	
	switch( Execute_Indication_API(iTrigger.sub_id, iTrigger.payload, iTrigger.len))
	{
	    case UL_S:
	    {
		       COM_PRINTI("In handle_p_iexec ->UL_S\n");
	    }
			break;
			
	    case UL_F:
	    {
		      COM_PRINTI("In handle_p_iexec --> UL_F Sending t_nack change state to p_ind_idle\n");
		      ind_exec_status = 0;
		      SetTransportTrigger(t_nack);
	    }
			break;
			
	    case UL_C:
	    {
		      COM_PRINTI("In handle_p_iexec --> UL_C Sending t_ack, change state to p_ind_idle\n");
		      ind_exec_status = 1;
		      SetTransportTrigger(t_ack);		      
	     }break;
	
	/*
	case UL_BUSY:
		ind_exec_status = ERR_VITAL_BUSY;
		SetTransportTrigger(t_nack);
	
	*/
	     default:
		   break;
	}
	
	return STATE_WAIT;
}

static int handle_p_bufferxfer(void)
{
	//called from handle_p_isend to handle bufferxfer
	//usageCount incremented in handle_p_isend
	//i_master   incremented in handle_p_isend

	COM_PRINTI("In handle_p_bufferxfer\n");
	startITimer(BTIME);
	Indication* ind = GetTManagerContext();
	ind->SUB_ID = iTrigger.sub_id;
	ind->ID = BUFFERTX;
	*((uint32_t *)&(ind->Data[0])) = (uint32_t)iTrigger.payload;
	*((uint16_t *)(&(ind->Data[4]))) = iTrigger.len;
	ind->size = 6;
	SetTransportTrigger(tx_event);
	return STATE_WAIT;
}

static uint16_t _mtu = 0;
static uint32_t tot_len = 0;
static uint16_t max_segments = 0;
static int handle_p_bufferxfer_rx(void)
{
	COM_PRINTI("In handle_p_bufferxfer_rx\n");
	CopyTrigger(&iTrigger,&temp);
	uint8_t *data_ptr = (uint8_t *)iTrigger.payload;
	int32_t ret = 0;

	if(data_ptr[CUR_SEG]== 0)
	{
		usageCount++;
		COM_PRINTI("In handle_p_bufferxfer_rx usage %d cr_master[%d] i_master[%d]\n", usageCount, cr_master, i_master);
		tot_len = (uint32_t)((data_ptr[TOTAL_LEN_MSB] << 16) | (data_ptr[TOTAL_LEN_LSB1] << 8) | data_ptr[TOTAL_LEN_LSB0]);
		_mtu    = (uint16_t)((data_ptr[MTU_MSB] << 8) | (data_ptr[MTU_LSB]));
		max_segments = (tot_len % _mtu)?(uint16_t)((tot_len / _mtu)+1):(uint16_t)(tot_len / _mtu);;
		_mtu = (tot_len < 24*1024)?(uint16_t)tot_len:(uint16_t)(data_ptr[MTU_MSB] << 8 | data_ptr[MTU_LSB]);
	}
	COM_PRINTI("Total length = %ld\n max_segments = %d\n", tot_len, max_segments);
	if(data_ptr[CUR_SEG] > (max_segments -1))
	{
		COM_PRINTI("In handle_p_bufferxfer_rx, sending t_nack. Change state to idle\n");
		SetTransportTrigger(t_nack);
		return p_ind_changeState(p_ind_idle);
	}
	else
	{
		ret = UpdateUpperLayerBuffer(data_ptr+ BUFF_TX_HDR, (uint16_t)(iTrigger.len-BUFF_TX_HDR));
		if(data_ptr[CUR_SEG] == max_segments -1 || ret == 1)
		{
			COM_PRINTI("In handle_p_bufferxfer_rx Change state to p_exec_bufferxfer\n");
			return p_ind_changeState(p_exec_bufferxfer);
		}
		else
		{
			COM_PRINTI("In handle_p_bufferxfer_rx. Sending t_ack  \n");
			SetTransportTrigger(t_ack);
			return STATE_WAIT;
		}
	}
	return STATE_WAIT;
}

static int handle_p_exec_bufferxfer(void)
{
	COM_PRINTI("In handle_p_exec_bufferxfer\r\n");
	switch( Execute_Bufferxfer_API(iTrigger.sub_id))
	{
	case UL_S://waiting for network response
	{
		COM_PRINTI("In handle_p_exec_bufferxfer --> UL_S\r\n");
		return (STATE_WAIT);
	}
	case UL_F://Wrong subid received for Buffer Tx
	{
		COM_PRINTI("In handle_p_exec_bufferxfer --> UL_F sending t_nack, change state to p_ind_msgfail \r\n");
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	case UL_C://Non network dependent subid's
	{
		COM_PRINTI("In handle_p_exec_bufferxfer --> UL_C sending t_ack, change state to p_ind_idle \r\n");
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	default: break;
	}
	return STATE_WAIT;
}


static int handle_p_cr_msgfail()
{//TODO handle negative scenario
	// CMD  peer is busy
	// IND  peer is busy
	// RESP peer timedout

	COM_PRINTI("In handle_p_cr_msgfail\n");
	NotifycrMsgFail();
	return p_cr_changeState(p_cr_idle);
}

static int handle_p_ind_msgfail()
{//TODO handle negative scenario
	// CMD  peer is busy
	// IND  peer is busy
	// RESP peer timedout

	COM_PRINTI("In handle_p_ind_msgfail\n");
	NotifyiMsgFail();
	return p_ind_changeState(p_ind_idle);
}

static int handle_p_cr_transpfail()
{
	COM_PRINTI("In handle_p_cr_transpfail  retry count %d state[%d]\n",crTrigger.retry,prevState_CR);

	if(crTrigger.retry > 0)
	{
		crTrigger.retry--;

		switch(prevState_CR)
		{
		case p_csend:
		{
			crTrigger.trigger = cmd_tx;
			crTrigger.payload = 0;
			crTrigger.len = 0;
			enqueueCmdTxToFront(&crTrigger);
			crTrigger.trigger = p_invalid;
		}
		break;

		case p_rsend:
		{

		}
		break;

		default: break;

		}
		return p_cr_changeState(prevState_CR);
	}
	else
	{
		NotifycrTrFail();
		return p_cr_changeState(p_cr_idle);
	}
}


static int handle_p_ind_transpfail()
{
	COM_PRINTI("In handle_p_ind_transpfail  retry count %d\n",iTrigger.retry);

	if(iTrigger.retry > 0)
	{
		iTrigger.trigger = ind_tx;
		iTrigger.retry--;
		enqueueIndTxToFront(&iTrigger);
		iTrigger.trigger = p_invalid;
		return p_ind_changeState(p_isend);
	}

	NotifyiTrFail();
	return p_ind_changeState(p_ind_idle);
}

static int handle_p_ind_invalid()
{
	COM_PRINTI("In handle_p_ind_invalid\n");
	return -1;
}
static int handle_p_cr_invalid()
{
	return -1;
}

static int p_cr_changeState(enum p_states state)
{

	COM_PRINTI("In p_cr_changeState \n");

	prevState_CR = curState_CR;
	curState_CR = state;
	return 1;
}

static int p_ind_changeState(enum p_states state)
{

	COM_PRINTI("In p_ind_changeState \n");

	prevState_I = curState_I;
	curState_I = state;
	return 1;
}

void CopyTrigger(pe_trigger* dtrigger,pe_trigger* strigger)
{
	if(strigger->trigger != p_invalid)
	{
		dtrigger->trigger = strigger->trigger;

		if(strigger->payload != NULL)
		{
			dtrigger->payload = strigger->payload;
		    dtrigger->len     = strigger->len;
		}

		if(strigger->sub_id != comm_NONE)
		{
			dtrigger->sub_id = strigger->sub_id;
		}
	}

}

static inline void UpdateTrigger(pe_trigger* trigger)
{
	memcpy(&temp,trigger,sizeof(pe_trigger));
	return;
}

uint32_t PrimitiveMain(pe_trigger* trigger)
{
	int32_t ret;
	UpdateTrigger(trigger);
	do
	{
		COM_PRINTI("PrimitiveMain %d \n",trigger->trigger);

		if((trigger->trigger != p_cr_change_state) && (trigger->trigger != p_ind_change_state))
		{
			currentEvent =  trigger->trigger;
		}

		ret = ptrigger_function[trigger->trigger]();
		if(ret == STATE_CHANGE)
		{
			if(IsCR(trigger->trigger))
			{
				COM_PRINTI("Changing CR state [%d] to [%d] Subid %x Data %p Len %d Retry %d\n",prevState_CR,curState_CR,crTrigger.sub_id,crTrigger.payload,crTrigger.len,crTrigger.retry);
				trigger->trigger = p_cr_change_state;

			}
			else if(IsInd(trigger->trigger))
			{
				COM_PRINTI("Changing Ind state [%d] to [%d] Subid %x Data %p Len %d Retry %d \n",prevState_I,curState_I,iTrigger.sub_id,iTrigger.payload,iTrigger.len,iTrigger.retry);
				trigger->trigger = p_ind_change_state;
			}
		}

	}while(ret == STATE_CHANGE);

	trigger->trigger = p_invalid;
	COM_PRINTI("PrimitiveMain exit\n");
	return 0;
}

int32_t UpdatePrimitiveTriggerRetry(pe_trigger* trigger)
{
	if(IsCR(trigger->trigger))
	{
		COM_PRINTI("UpdateTriggerRetry -->CR %d %d %p %d\r\n", trigger->trigger, trigger->sub_id, trigger->payload, trigger->len);
		return enqueueCmdTxToFront(trigger);
	}
	if(IsInd(trigger->trigger))
	{
		COM_PRINTI("UpdateTriggerRetry -->Ind %d %d %p %d\r\n", trigger->trigger, trigger->sub_id, trigger->payload, trigger->len);
		return enqueueIndTxToFront(trigger);
	}
	return 0;
}

void SetPrimitiveTrigger_Data(enum _ptrigger _trigger,uint8_t* data,uint16_t len,enum comm_subid subid)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.len		= len;
	trigger.sub_id  = subid;

	COM_PRINTI("In SetPrimitiveTrigger_Data ->Trigger %d payload %p length %d subid %d\n",trigger.trigger,trigger.payload,trigger.len,trigger.sub_id);

	switch(_trigger)
	{
	case ind_r:
	case ind_s:
	case ind_f:
	case ind_ack_s:
	case ind_nack_s:
	case bufferxfer_rx:
	case bufferxfer_s:
	case bufferxfer_f:
	case ind_transp_f:
		memcpy(&irtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	case cmd_f:
	case cmd_s:
	case resp_r:
	case cr_ack_s:
	case cr_nack_s:
	case cmd_r:
	case rsp_s:
	case rsp_f:
	case cr_transp_f:
		memcpy(&crtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	default:
		break;
	}
}

void SetPrimitiveTrigger_UL(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid, uint8_t retry)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.sub_id  = subid;
	trigger.len		= len;
	trigger.retry   = retry;

	COM_PRINTI("In SetPrimitiveTrigger_UL trigger[%d] payload[%p] len[%d] sub_id[%d]\n", trigger.trigger, trigger.payload, trigger.len, trigger.sub_id);

	if(_trigger == cmd_tx)
	{
		enqueueCmdTx(&trigger); // push to command/response queue.

	}
	else if(_trigger == ind_tx || _trigger == buff_tx)
	{
		enqueueIndTx(&trigger);// push to indication queue.
	}
	else if(_trigger == cr_exec_s ||_trigger == cr_exec_f )
	{
		memcpy(&crExecTrigger, &trigger, sizeof(pe_trigger));
	}
}

void SetPrimitiveTrigger_RT(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.sub_id  = subid;
	trigger.len		= len;

	switch(_trigger)
	{
	case p_ind_timeout:
	case ind_exec_s:
	case ind_exec_f:
	case bufferxfer_exec_s:
	case bufferxfer_exec_f:
		memcpy(&irtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;

	case p_cr_timeout:
	case cr_exec_s:
	case cr_exec_f:
		memcpy(&crtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	default:
		return;
	}

}

uint32_t GetPrimitiveTrigger(pe_trigger* trigger)
{
	if(irtTrigger.trigger!= p_invalid) // No pending realtime triggers.
	{
		memcpy(trigger, &irtTrigger, sizeof(pe_trigger));
		irtTrigger.trigger = p_invalid;
		return 1;
	}
	else if(crtTrigger.trigger!= p_invalid) // No pending realtime triggers.
	{
		memcpy(trigger, &crtTrigger, sizeof(pe_trigger));
		crtTrigger.trigger = p_invalid;
		return 1;
	}
	else if(!GetTransportManagerState())
	{
		if(crExecTrigger.trigger != p_invalid )
		{
			if(curState_CR == p_cexec && (crExecTrigger.trigger ==cr_exec_s || crExecTrigger.trigger ==cr_exec_f ))
			{
				memcpy(trigger, &crExecTrigger, sizeof(pe_trigger));
				crExecTrigger.trigger = p_invalid;
				return 1;
			}

		}
		else
		{
			if(curState_CR == p_cr_idle && !isCmdTxEmpty())
			{
				trigger->trigger = cmd_tx;
				return 1;
			}
			else if(curState_I == p_ind_idle && !isIndTxEmpty())
			{
				trigger->trigger = ind_tx;
				return 1;
			}
		}
	}
	else
	{
		return 0;
	}
	return 0;
}

static int IsCR(enum _ptrigger _trigger)
{
	COM_PRINTI("In IsCR, Cr trigger:%d\n", _trigger);
	switch(_trigger)
	{
	case cmd_tx:
	case cmd_f:
	case cmd_s:
	case resp_r:
	case cr_ack_s:
	case cr_nack_s:
	case cr_exec_s:
	case cr_exec_f:
	case cmd_r:
	case rsp_s:
	case rsp_f:
	case cr_transp_f:
	case p_cr_change_state:
	case p_cr_timeout:
		COM_PRINTI("In IsCR, valid Cr trigger:%d\r\n", _trigger);
			return 1;
	default:
		return 0;
	}
}

static int IsInd(enum _ptrigger _trigger)
{
	COM_PRINTI("In IsInd Invalid indication trigger:%d\n", _trigger);
	switch(_trigger)
	{
	case ind_tx:
	case ind_r:
	case ind_s:
	case ind_f:
	case ind_ack_s:
	case ind_nack_s:
	case ind_exec_s:
	case ind_exec_f:
	case ind_transp_f:
	case p_ind_change_state:
	case p_ind_timeout:
	case buff_tx:
	case bufferxfer_rx:
	case bufferxfer_s:	  //  indication bulk success
	case bufferxfer_f:
		COM_PRINTI("In IsInd valid indication trigger:%d\n", _trigger);
		return 1;
	default:
		return 0;
	}
}

int isPrimitiveIdle(void)
{
	if(curState_CR == 1 && curState_I ==1)
	{
		return 1;
	}
	return 0;
}

int32_t resetPrimitiveManagerCR(void)
{
	crTrigger.sub_id = comm_NONE;
	crTrigger.trigger = p_invalid;
	crTrigger.payload= NULL;
	crTrigger.len    = 0;

	crtTrigger.sub_id = comm_NONE;
	crtTrigger.trigger = p_invalid;
	crtTrigger.payload= NULL;
	crtTrigger.len    = 0;

	crExecTrigger.sub_id = comm_NONE;
	crExecTrigger.trigger = p_invalid;
	crExecTrigger.payload= NULL;
	crExecTrigger.len    = 0;

	curState_CR = p_cr_idle;
	prevState_CR = p_cr_idle;

	cr_exec_status = 0;

	if(usageCount > 0) usageCount-=1;
	cr_master = 0;

	stopCRTimer();

	return 1;
}

int32_t resetPrimitiveManagerInd(void)
{
	iTrigger.sub_id = comm_NONE;
	iTrigger.trigger = p_invalid;
	iTrigger.payload= NULL;
	iTrigger.len    = 0;

	irtTrigger.sub_id = comm_NONE;
	irtTrigger.trigger = p_invalid;
	irtTrigger.payload= NULL;
	irtTrigger.len    = 0;

	curState_I = p_ind_idle;
	prevState_I = p_ind_idle;

	ind_exec_status = 0;

	if(usageCount > 0) usageCount-=1;

	i_master = 0;

	stopITimer();

	return 1;
}

int32_t timeoutPrimitiveManager(void)
{
	int32_t CR = 0;
	int32_t Ind =0;

	COM_PRINTI("Inside timeoutPrimitiveManager: curState_I[%d] curState_CR[%d] \n",curState_I,curState_CR);

	switch(curState_I)
	{
	case p_irecv:
	case p_iexec:
	case p_exec_bufferxfer:
	case p_bufferxfer_rx:
		Ind = resetPrimitiveManagerInd();
		goto exit;
		break;
	default: break;
	}

	switch(curState_CR)
	{
	case p_crecv:
	case p_cexec:
	case p_rsend:
		CR = resetPrimitiveManagerCR();
		break;
	default: break;
	}


exit:
	return (CR|Ind)?1:0;
}

int32_t resetPrimitiveManager(void)
{
	resetPrimitiveManagerCR();
	resetPrimitiveManagerInd();
	return 0;
}

# define TrFailTimeout 10
int32_t resetPrimitiveMaster(enum _Exchange_Type primitive)
{
	if(primitive == COMMAND)
	{
		stopCRTimer();
		startCRTimer(TrFailTimeout);
	}
	else if (primitive == INDICATION)
	{
		stopITimer();
		startITimer(TrFailTimeout);
	}
	return 0;
}