# include "comm_task.h"
# include "Comm_Manager.h"

ke_state_t comm_state[COMM_IDX_MAX] __SECTION_ZERO("retention_mem_area0"); //RETENTION MEMORY

int comm_manager(ke_msg_id_t const msgid,void const *param,ke_task_id_t const dest_id,ke_task_id_t const src_id);

/* Default State handlers definition. */
const struct ke_msg_handler comm_default_state[] =
{
    {KE_MSG_DEFAULT_HANDLER,                (ke_msg_func_t)comm_manager},
};
		

const struct ke_state_handler comm_handler 	  					= KE_STATE_HANDLER(comm_default_state);
/// Application Task Descriptor
static const struct ke_task_desc TASK_DESC_COMM = {NULL,
                                                  &comm_handler,
                                                  comm_state,
                                                  1,
                                                  COMM_IDX_MAX};		

static timer_hnd crTimer __SECTION_ZERO("retention_mem_area0");
static timer_hnd indTimer __SECTION_ZERO("retention_mem_area0");
																									
static Comm_Queue_t  com_queue;

void comm_init(void)
{
    // Create comm task		
		ke_task_create(TASK_COMM, &TASK_DESC_COMM);
		InitQueue();
		InitTriggerQueue();
		resetPrimitiveManager();
		resetTransportManager();
		crTimer				= EASY_TIMER_INVALID_TIMER;
		indTimer			= EASY_TIMER_INVALID_TIMER;
    // Initialize Task state
    // ke_state_set(TASK_COMM, COMM_DB_INIT);
	
}


int comm_manager(ke_msg_id_t const msgid,void const *param,ke_task_id_t const dest_id,ke_task_id_t const src_id)
{
	memcpy(&com_queue, param, sizeof(com_queue));
	RunMainLoop(com_queue);
	return (KE_MSG_CONSUMED);
}
void crTimeoutCallback(void)
{
	crTimer = EASY_TIMER_INVALID_TIMER;
	Comm_Queue_t queue;
	queue.module 			= ul_rt;
	queue.event.pt.trigger  = p_cr_timeout;
	queue.event.pt.len 		= 0;
	queue.event.pt.payload 	= 0;
	queue.event.pt.sub_id	= comm_NONE;
	triggerCommMngr(&queue);
}

void iTimeoutCallback(void)
{
	indTimer = EASY_TIMER_INVALID_TIMER;
	Comm_Queue_t queue;
	queue.module 			= ul_rt;
	queue.event.pt.trigger  = p_ind_timeout;
	queue.event.pt.len 		= 0;
	queue.event.pt.payload 	= 0;
	queue.event.pt.sub_id	= comm_NONE;
	triggerCommMngr(&queue);
}
uint32_t startITimer(uint32_t period)
{
	if(indTimer!=EASY_TIMER_INVALID_TIMER)
	{
		indTimer = app_easy_timer_modify(indTimer, period);
	}
	else
	{
		indTimer = app_easy_timer(period, iTimeoutCallback);
	}
	if(indTimer!=EASY_TIMER_INVALID_TIMER)
	{
		return 1;
	}
	return 0;
}

uint32_t stopITimer(void)
{
	if(indTimer!=EASY_TIMER_INVALID_TIMER)
	{
		app_easy_timer_cancel(indTimer);
		indTimer = EASY_TIMER_INVALID_TIMER;
		return 1;
	}
	return 0;
}

uint32_t startCRTimer(uint32_t period)
{
	if(indTimer!=EASY_TIMER_INVALID_TIMER)
	{
		crTimer = app_easy_timer_modify(crTimer, period);
	}
	else
	{
		crTimer = app_easy_timer(period, crTimeoutCallback);
	}
	if(crTimer!=EASY_TIMER_INVALID_TIMER)
	{
		return 1;
	}
	return 0;
}

uint32_t stopCRTimer(void)
{
	if(crTimer!=EASY_TIMER_INVALID_TIMER)
	{
		app_easy_timer_cancel(crTimer);
		crTimer = EASY_TIMER_INVALID_TIMER;
		return 1;
	}
	return 0;
}

void triggerCommMngr(Comm_Queue_t* msg)
{
	Comm_Queue_t *ptr = ke_msg_alloc(KE_MSG_DEFAULT_HANDLER, TASK_COMM, NULL, sizeof(Comm_Queue_t));
	memcpy(ptr, msg, sizeof(Comm_Queue_t));
	ke_msg_send(ptr);
}

void triggerFromPrimitive(pe_trigger* retrans)
{
	Comm_Queue_t msg;

	msg.module = m_self;
	//memcpy(&msg.event.pt,retrans,sizeof(pe_trigger));

	msg.event.pt.sub_id   = retrans->sub_id;
	msg.event.pt.payload  = 0;
	msg.event.pt.trigger  = retrans->trigger;
	msg.event.pt.len      = 0;
	msg.event.pt.retry    = retrans->retry;

	//xQueueSendToFront(Comm_Queue_Handle, &msg, 1000); //TODO
	triggerCommMngr(&msg);

}

uint32_t IsComMessageEmpty()
{
	return 1;
} // TODO thios needs to ne checked

//TODO sleep to be implemented
void Comm_Can_Sleep(){}
void Comm_Sleep_Set(){}
void Comm_Sleep_Clear(){}
void Comm_Sleep_Clear_isr(){}
void Handle_Sleep(void){}
