/*
 * PrimitiveManager.c
 *
 *  Created on: 09-Sep-2023
 *      Author: admin
 */
#include"User_Application.h"
#include"Comm_Manager.h"
#include"PrimitiveQueue.h"

# define ENABLE_CLOSE_STATE 1

# define Execute_Command_API  BLE_ExecuteCommand
# define Execute_Response_API  BLE_ExecuteResponse
# define Execute_Indication_API  BLE_ExecuteIndication
# define Execute_Bufferxfer_API 	BLE_ExecuteBufferxfer

#pragma pack(1)
struct Buffer_Xfer
{
	uint8_t *payload;
	uint8_t  current_segment;
	uint8_t  max_segments; //Total number of segments to be sent in buffer transfer data
	uint16_t mtu;
	uint32_t tot_len;
	uint32_t timestamp;
	uint8_t  requester;
	uint32_t crc;
};

static enum exec_req BLE_ExecuteCommand(enum comm_subid sub_id, uint8_t* data, uint16_t len);
static enum exec_req BLE_ExecuteResponse(enum comm_subid sub_id, uint8_t* data, uint16_t len);
static enum exec_req BLE_ExecuteIndication(enum comm_subid sub_id, uint8_t* data, uint16_t len);
static enum exec_req BLE_ExecuteBufferxfer(enum comm_subid sub_id, uint8_t *data, uint16_t len, uint8_t seqNo, uint32_t crc);

static pe_trigger iTrigger       = {comm_NONE, p_invalid, 0, 0,0};    // current indication trigger
static pe_trigger crTrigger      = {comm_NONE, p_invalid, 0, 0,0};     // current command response trigger
static pe_trigger irtTrigger     = {comm_NONE, p_invalid, 0, 0,0};    // indication real time triggers
static pe_trigger crtTrigger     = {comm_NONE, p_invalid, 0, 0,0};     // command response real time triggers
static pe_trigger crExecTrigger  = {comm_NONE, p_invalid, 0, 0,0};     // non realtime execute triggers from Commands
static pe_trigger temp           = {comm_NONE, p_invalid, 0, 0,0};

static enum _ptrigger currentEvent = p_ind_change_state;

int32_t resetPrimitiveManagerCR(void);
int32_t resetPrimitiveManagerInd(void);
void CopyTrigger(pe_trigger* dtrigger,pe_trigger* strigger);

#if ENABLE_CLOSE_STATE
static char usageCount = 0;
static char cr_master = 0;
static char i_master = 0;
#endif
static char pendTimeout = 0;

#define CUR_SEG 			   0x00
#define MTU_LSB 			   0x01
#define MTU_MSB 			   0x02
#define TOTAL_LEN_LSB0 		   0x03
#define	TOTAL_LEN_LSB1 		   0x04
#define TOTAL_LEN_MSB 		   0x05
#define TIMESTAMP_LSB0		   0x06
#define TIMESTAMP_LSB1		   0x07
#define TIMESTAMP_MSB0		   0x08
#define TIMESTAMP_MSB1		   0x09
#define DATA_ORIGIN			   0x0A
#define CRC_LSB0		       0x0B
#define CRC_LSB1		       0x0C
#define CRC_MSB0		       0x0D
#define CRC_MSB1		       0x0E
#define BUFF_TX_HDR 15


static struct Buffer_Xfer bufferxfer_context;
static int cmd_tx_event(void);
static int cmd_f_event(void);
static int cmd_s_event(void);
static int resp_r_event(void);
static int cr_ack_s_event(void);
static int cr_nack_s_event(void);
static int cmd_r_event(void);
static int rsp_s_event(void);
static int rsp_f_event(void);
static int ind_tx_event(void);
static int ind_r_event(void);
static int ind_s_event(void);
static int ind_f_event(void);
static int ind_ack_s_event(void);
static int ind_nack_s_event(void);
static int cr_exec_s_event(void);
static int cr_exec_f_event(void);
static int ind_exec_s_event(void);
static int ind_exec_f_event(void);
static int cr_transp_f_event(void);
static int ind_transp_f_event(void);
static int bufferxfer_s_event(void);
static int bufferxfer_f_event(void);
static int bufferxfer_exec_f_event(void);
static int bufferxfer_exec_s_event(void);
static int handle_cr_change_state_event(void);
static int handle_ind_change_state_event(void);
static int bufferxfer_event(void);
static int bufferxfer_rx_event(void);
static int handle_cr_timeout_event(void);
static int handle_ind_timeout_event(void);
static int handle_invalid_event(void);

static void WriteCmd (struct _Exchange* pcommand, enum comm_subid c_subid);
static void MakePacket(struct _Exchange *packet, uint8_t *data, uint16_t len);

typedef int (*_ptrigger_function)();

static const _ptrigger_function ptrigger_function[]={
		cmd_tx_event,								// command tx event
		cmd_f_event,								// Command fail event
		cmd_s_event,								// command sucess event
		resp_r_event,								// response received event
		cr_ack_s_event, 							// command response ack sent event
		cr_nack_s_event,							// command response nack sent event
		cr_exec_s_event,							// command/response execute success event
		cr_exec_f_event,							// command/response execute fail event
		cmd_r_event,								//command received event
		rsp_s_event,								// response sent event
		rsp_f_event,								// response fail event
		cr_transp_f_event, 							// command/response transport fail event
		handle_cr_change_state_event,   			// command/response change state event
		handle_cr_timeout_event,							// command/response timeout event
		ind_tx_event,								// indication tx event
		ind_r_event,								// indication rx event
		ind_s_event,								// indication sent event
		ind_f_event,								// indication fail event
		ind_ack_s_event,  							// indication ack sent event
		ind_nack_s_event, 							// indication nack sent event
		ind_exec_s_event, 							// indication execute success event
		ind_exec_f_event, 							// indication execute fail event
		bufferxfer_event,
		bufferxfer_rx_event, 					// buff rx event
		bufferxfer_s_event,							// Bulk success event
		bufferxfer_f_event,							// Bulk fail event
		bufferxfer_exec_s_event,							// bufferxfer execute success
		bufferxfer_exec_f_event,							// bufferxfer execute fail
		ind_transp_f_event,							// indication transport fail event
		handle_ind_change_state_event,  			// indication change state event
		handle_ind_timeout_event,							// indication timeout event
		handle_invalid_event
};



enum p_states
{
	p_cr_idle,       //idle
	p_csend,      // command send
	p_csuccess,   // command send success
	p_rrecv,      // response received
	p_rexec,      // response executed
	p_crecv,      // command receive
	p_cexec,      // command execute
	p_rsend,      // response sent
	p_cr_tout,	  // Command/response Timeout
	p_cr_msgfail,    // command/response message fail
	p_cr_transpfail,  // command/response transport fail
	p_cr_invalidstate, // command/reponse invalid
	p_ind_idle,
	p_isend,      // indication send
	p_irecv,      // indication receive
	p_iexec,      // indication executed
	p_bufferxfer,			// Send Bulk transfer
	p_bufferxfer_rx,		// Buffer transfer rx
	p_exec_bufferxfer,		// Execute buffer transfer
	p_ind_touts,			// Indication/bufferxFer timeout
	p_ind_msgfail,    // message fail - nack
	p_ind_transpfail,  // transport fail
	p_ind_invalidstate

};


static enum p_states curState_CR = p_cr_idle;
static enum p_states prevState_CR = p_cr_idle;

static enum p_states curState_I = p_ind_idle;
static enum p_states prevState_I = p_ind_idle;

static int p_cr_changeState(enum p_states state);
static int p_ind_changeState(enum p_states state);

static int IsCR(enum _ptrigger _trigger);
static int IsInd(enum _ptrigger _trigger);

//static int32_t cr_exec_status  = 0; //
//static int32_t ind_exec_status = 0;

static int handle_p_cr_idle(void);
static int handle_p_csend(void);
static int handle_p_csuccess(void);
static int handle_p_rrecv(void);
static int handle_p_rexec(void);
static int handle_p_crecv(void);
static int handle_p_cexec(void);
static int handle_p_rsend(void);
static int handle_p_isend(void);
static int handle_p_irecv(void);
static int handle_p_iexec(void);
static int handle_p_cr_msgfail(void);
static int handle_p_bufferxfer(void);
static int handle_p_bufferxfer_rx(void);
static int handle_p_exec_bufferxfer(void);
static int handle_p_cr_transpfail(void);

static int handle_p_ind_msgfail(void);
static int handle_p_ind_transpfail(void);
static int handle_p_ind_idle(void);
static int handle_p_ind_invalid(void);

static int handle_p_cr_timeout(void);
static int handle_p_ind_timeout(void);
static int handle_p_cr_invalid(void);


typedef int (*_pstate_function)(void);

static const _pstate_function pstate_function[]={
		handle_p_cr_idle,
		handle_p_csend,
		handle_p_csuccess,
		handle_p_rrecv,
		handle_p_rexec,
		handle_p_crecv,
		handle_p_cexec,
		handle_p_rsend,
		handle_p_cr_timeout,
		handle_p_cr_msgfail,
		handle_p_cr_transpfail,
		handle_p_cr_invalid,
		// indications
		handle_p_ind_idle,
		handle_p_isend,
		handle_p_irecv,
		handle_p_iexec,
		handle_p_bufferxfer,
		handle_p_bufferxfer_rx,
		handle_p_exec_bufferxfer,
		handle_p_ind_timeout,
		handle_p_ind_msgfail,
		handle_p_ind_transpfail,
		handle_p_ind_invalid
};

static int cmd_tx_event()
{
	if(curState_CR == p_cr_idle)
	{
		return p_cr_changeState(p_csend);
	}
	return -1;
}

static int cmd_f_event()
{
	if(curState_CR == p_csend)
	{
	return p_cr_changeState(p_cr_msgfail);
	}
	return -1;
}

static int cmd_s_event()
{
	if(curState_CR == p_csend)
	{
	return p_cr_changeState(p_csuccess);
	}
	return -1;

}

static int resp_r_event()
{
	if(curState_CR == p_csuccess)
	{
		return p_cr_changeState(p_rrecv);
	}
	else
	{
		SetTransportTrigger(t_nack);
		return(STATE_WAIT);
	}
}

static int cr_ack_s_event()
{
	if(curState_CR == p_rexec )
	{
		return p_cr_changeState(p_cr_idle);
	}
	else if (curState_CR == p_crecv)  // command recv state is not handing ack sent event
	{
		return p_cr_changeState(p_cexec);
	}
	return -1;
}
static int cr_nack_s_event()
{

	if(curState_CR == p_rexec )
	{
		return p_cr_changeState(p_cr_msgfail);
	}
	else if (curState_CR == p_crecv)  // command recv state is not handing ack sent event
	{
		return p_cr_changeState(p_cr_idle);
	}
	return -1;
}
static int cmd_r_event()
{
	if(curState_CR == p_cr_idle)
	{
		return p_cr_changeState(p_crecv);
	}
	else
	{
		if(crTrigger.sub_id == temp.sub_id)
		{
			resetPrimitiveManagerCR();
//			memcpy(&crTrigger,&temp,sizeof(pe_trigger));
			return p_cr_changeState(p_crecv);
		}
		else
			SetTransportTrigger(t_nack);
	}
	return -1;
}

static int rsp_s_event()
{
	if(curState_CR == p_rsend)
	{
		return p_cr_changeState(p_cr_idle);
	}
	return -1;
}
static int rsp_f_event()
{
	if(curState_CR == p_rsend)
	{
		return p_cr_changeState(p_cr_msgfail);
	}
	return -1;
}

static int ind_tx_event()
{
	if(curState_I == p_ind_idle)
	{
		return p_ind_changeState(p_isend);
	}
	return -1;
}

static int ind_r_event()
{
	if(curState_I == p_ind_idle)
	{
		return p_ind_changeState(p_irecv);
	}
	else
	{
		SetTransportTrigger(t_nack);
		return(STATE_WAIT);
	}
}

static int ind_s_event()
{
	if(curState_I == p_isend)
	{
//		TestComplete();
		if(iTrigger.sub_id == comm_wUconfig && ((uint8_t*)iTrigger.payload)[0] == 1)
		{
			handleUserConfigAck(1);
		}
		return p_ind_changeState(p_ind_idle);
	}
	return -1;
}

static int ind_f_event()
{
	if(curState_I == p_isend)
	{
		if(iTrigger.sub_id == comm_wUconfig && ((uint8_t*)iTrigger.payload)[0] == 1)
		{
			handleUserConfigAck(0);
		}
		return p_ind_changeState(p_ind_msgfail);
	}
	return -1;
}

static int ind_ack_s_event()
{
	if(curState_I == p_iexec)
	{
		return p_ind_changeState(p_ind_idle);
	}
	else if(curState_I == p_exec_bufferxfer)
	{
		return p_ind_changeState(p_ind_idle);
	}

	return -1;
}

static int ind_nack_s_event()
{
	if(curState_I == p_iexec)
		{
			return p_ind_changeState(p_ind_msgfail);
		}
		else if(curState_I == p_exec_bufferxfer)
		{
			return p_ind_changeState(p_ind_msgfail);
		}

		return -1;
}
static int cr_exec_s_event()
{
	//cr_exec_status = 1;
	if(curState_CR == p_rexec )
	{
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	else if( curState_CR == p_cexec)
	{
		return p_cr_changeState(p_rsend);
	}
	return -1;
}

static int cr_exec_f_event()
{
	//cr_exec_status = 0;
	if(curState_CR == p_rexec )
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	else if(curState_CR == p_cexec)
	{
		return p_cr_changeState(p_rsend);
	}
	return -1;
}
static int ind_exec_s_event()
{
	//ind_exec_status = 1;
	if(curState_I == p_iexec )
	{
		SetTransportTrigger(t_ack);
		return STATE_WAIT;
	}
	return -1;
}
static int ind_exec_f_event()
{
	//ind_exec_status = 0;
	if(curState_I == p_iexec)
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	return -1;
}

static int ind_transp_f_event()
{

	return p_ind_changeState(p_ind_transpfail);
}
static int bufferxfer_event(void)
{
	return p_ind_changeState(p_bufferxfer);
}

static int bufferxfer_s_event()
{
	return p_ind_changeState(p_ind_idle);
}

static int bufferxfer_f_event()
{
	return p_ind_changeState(p_ind_msgfail);
}

static int bufferxfer_exec_s_event()
{
	//ind_exec_status = 1;
	if(curState_I == p_exec_bufferxfer)
	{
		if(bufferxfer_context.current_segment == bufferxfer_context.max_segments - 1)
		{
			SetTransportTrigger(t_ack);
		}
		else
		{
			SetTransportTrigger(t_buff_cont);
		}
		return STATE_WAIT;
	}
	return -1;

}

static int bufferxfer_exec_f_event()
{
	//ind_exec_status = 0;
	if(curState_I == p_exec_bufferxfer)
	{
		SetTransportTrigger(t_nack);
		return STATE_WAIT;
	}
	return -1;
}

static int handle_ind_change_state_event(void)
{
	int ret;
#ifdef DEBUG_ENABLE
	uint8_t msg[3] = {'P','I','X'};
	msg[2] = curState_I;
	Send_To_Gatt_Client(msg,3, 0x16, NULL); //NODO: debug log
#endif
	ret = pstate_function[curState_I]();
	return ret;
}
static int handle_ind_timeout_event(void)
{
	return p_ind_changeState(p_ind_touts);
}

static int handle_invalid_event()
{
	return -1;
}
static int cr_transp_f_event()
{
	return p_cr_changeState(p_cr_transpfail);
}

static int handle_cr_change_state_event(void)
{
	int ret;
#ifdef DEBUG_ENABLE
	uint8_t msg[3] = {'P','C','X'};
	msg[2] = curState_CR;
	Send_To_Gatt_Client(msg,3, 0x16, NULL); //NODO: debug log
#endif
	ret = pstate_function[curState_CR]();
	return ret;
}

static int handle_cr_timeout_event(void)
{
	return p_cr_changeState(p_cr_tout);
}

static int handle_p_cr_timeout(void)
{
	if(crTrigger.retry > 0)
	{
		crTrigger.trigger = cmd_tx;
		crTrigger.payload = 0;
		crTrigger.len = 0;
		crTrigger.retry--;
		triggerFromPrimitive(&crTrigger);
		crTrigger.trigger = p_invalid;
	}

	if(curState_I == p_ind_idle)
	{
		SetTransportTrigger(t_timeout);
	}
	else
	{
		pendTimeout = 1;
	}
	return p_cr_changeState(p_cr_idle);
}

static int handle_p_ind_timeout(void)
{
	if(iTrigger.retry > 0)
	{
		iTrigger.trigger = ind_tx;
		iTrigger.retry--;
		triggerFromPrimitive(&iTrigger);
		iTrigger.trigger = p_invalid;
	}

	SetTransportTrigger(t_timeout);
	
	return p_ind_changeState(p_ind_idle);
}

static int handle_p_cr_idle()
{
	//cr_exec_status = 0;
	#if ENABLE_CLOSE_STATE
	if(usageCount>0)
	{
		--usageCount;
	}
	else
	{
		//error usageCount negative
	}
	if(usageCount == 0 && cr_master )
	{
		if(isTxEmpty(cmd_tx) && isTxEmpty(ind_tx))
		{
			cr_master = 0;
			if(prevState_CR != p_cr_tout)
			{
				stopCRTimer();
				SetTransportTrigger(t_close_event);
			}
		}
	}
#endif

	crTrigger.payload = 0;
	crTrigger.sub_id  = comm_NONE;
	crTrigger.trigger = p_invalid;
	crTrigger.len     = 0;
	crTrigger.retry   = 0;

	return(STATE_WAIT);
}

static int handle_p_csend()
{
#if ENABLE_CLOSE_STATE
	cr_master = 1;
	usageCount++;
#endif
	startCRTimer(CRTIME);
	dequeueTx(&crTrigger, cmd_tx);
	WriteCmd(GetTManagerContext(), crTrigger.sub_id);
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}

static int handle_p_csuccess()
{
	return(STATE_WAIT);
}

//static enum _ptrigger ackTrigger;
static int handle_p_rrecv()
{
	
	CopyTrigger(&crTrigger,&temp);
	//TODO send the response to upper layer
	//ackTrigger = cr_exec_s; // Testing Always success TODO remove added for testing
	return p_cr_changeState(p_rexec); //  ST implementaion testing
}

static int handle_p_rexec()
{
	switch( Execute_Response_API(crTrigger.sub_id, (uint8_t *)crTrigger.payload, crTrigger.len))
	{
	case UL_S:
		return (STATE_WAIT);
	case UL_F:
		SetTransportTrigger(t_nack);
		return p_cr_changeState(p_cr_msgfail);
	case UL_C:
		SetTransportTrigger(t_ack);
		return (STATE_WAIT);
	}
	return -1;
}

static int handle_p_crecv()
{
	//TODO for vitals, check if the sensor task is busy. If sensor task is free then lock it and send ack else nack.
	//TODO check the status of upper layer . if it is serviceable and valid subid send ACK else send NACK to transport layer
#if ENABLE_CLOSE_STATE
	usageCount++;
#endif
	CopyTrigger(&crTrigger,&temp);
	SetTransportTrigger(t_ack);
	return(STATE_WAIT);
}



static int handle_p_cexec()
{
	switch(Execute_Command_API(crTrigger.sub_id, crTrigger.payload, crTrigger.len))
	{
	case UL_S: 
		return(STATE_WAIT);
	case UL_F:
		return p_cr_changeState(p_cr_msgfail);
	case UL_C:
		return p_cr_changeState(p_rsend);
	}
	return 0;
}

static int handle_p_rsend()
{
	Response* resp = GetTManagerContext();
	resp->ID = RESPONSE;
	resp->SUB_ID = crTrigger.sub_id;
	MakePacket(resp, crTrigger.payload, crTrigger.len);
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}

static int handle_p_isend()
{
#if ENABLE_CLOSE_STATE
	i_master = 1;
	usageCount++;
#endif
	
	startITimer(ITIME);
	dequeueTx(&iTrigger, ind_tx);
	if(iTrigger.trigger == buff_tx)
			return p_ind_changeState(p_bufferxfer);
	Indication* ind = GetTManagerContext();
	ind->ID = INDICATION;
	ind->SUB_ID = iTrigger.sub_id;
	MakePacket(ind, iTrigger.payload, iTrigger.len);
	SetTransportTrigger(tx_event);
	return(STATE_WAIT);
}

static int handle_p_irecv()
{
#if ENABLE_CLOSE_STATE
	usageCount++;
#endif
	CopyTrigger(&iTrigger,&temp);
	//TODO message to upper layer
	//ackTrigger = ind_exec_s; // Testing Always success
	return p_ind_changeState(p_iexec); //  ST implementaion testing

	//return(STATE_WAIT);
}

static int handle_p_iexec()
{
	switch( Execute_Indication_API(iTrigger.sub_id, iTrigger.payload, iTrigger.len))
	{
		case UL_S:
			return (STATE_WAIT);
		case UL_F:
		{
			SetTransportTrigger(t_nack);
			return p_ind_changeState(p_ind_msgfail);
		}
		case UL_C:
		{
			SetTransportTrigger(t_ack);
			return (STATE_WAIT);
		}
	}
	return -1;
}
static int handle_p_bufferxfer(void)
{
	// called from handle_p_isend to handle bufferxfer
	//usageCount incremented in handle_p_isend
	//i_master   incremented in handle_p_isend

	Indication* ind = GetTManagerContext();
	ind->SUB_ID = iTrigger.sub_id;
	ind->ID = BUFFERTX;
	*((uint32_t *)&(ind->Data[0])) = (uint32_t)iTrigger.payload;
	*((uint16_t *)(&(ind->Data[4]))) = iTrigger.len;
	ind->size = 6;
	SetTransportTrigger(tx_event);
	return STATE_WAIT;

}
static int bufferxfer_rx_event()
{
	if(curState_I == p_exec_bufferxfer || curState_I == p_ind_idle)
	{
		return p_ind_changeState(p_bufferxfer_rx);
	}
	return -1;
}
static int handle_p_bufferxfer_rx(void)
{
#if ENABLE_CLOSE_STATE
		usageCount++;
#endif
	
	CopyTrigger(&iTrigger,&temp);

	if(((uint8_t *)iTrigger.payload)[CUR_SEG] == 0)
	{
		if(bufferxfer_context.max_segments == 0)
		{
			bufferxfer_context.current_segment = 0;
			bufferxfer_context.mtu 	= ((uint8_t *)iTrigger.payload)[MTU_LSB] |
																(((uint8_t *)iTrigger.payload)[MTU_MSB]<<8);
			bufferxfer_context.tot_len 	= ((uint8_t *)iTrigger.payload)[TOTAL_LEN_LSB0] |
																		(((uint8_t *)iTrigger.payload)[TOTAL_LEN_LSB1]<<8) |
																		(((uint8_t *)iTrigger.payload)[TOTAL_LEN_MSB]<<16);
			bufferxfer_context.max_segments = (bufferxfer_context.tot_len / bufferxfer_context.mtu) + ((bufferxfer_context.tot_len % bufferxfer_context.mtu)>0?1:0);
			bufferxfer_context.timestamp 	= ((uint8_t *)iTrigger.payload)[TIMESTAMP_LSB0] |
																			(((uint8_t *)iTrigger.payload)[TIMESTAMP_LSB1]<<8) |
																			(((uint8_t *)iTrigger.payload)[TIMESTAMP_MSB0]<<16) |
																			(((uint8_t *)iTrigger.payload)[TIMESTAMP_MSB1]<<24);
			bufferxfer_context.requester = ((uint8_t *)iTrigger.payload)[DATA_ORIGIN];
			bufferxfer_context.crc 	= ((uint8_t *)iTrigger.payload)[CRC_LSB0] |
																(((uint8_t *)iTrigger.payload)[CRC_LSB1]<<8) |
																(((uint8_t *)iTrigger.payload)[CRC_MSB0]<<16) |
																(((uint8_t *)iTrigger.payload)[CRC_MSB1]<<24);
		}
		else
		{
			// HAndle error.
			SetTransportTrigger(t_nack);
			return p_ind_changeState(p_ind_msgfail);
		}

	}
	else
	{
		bufferxfer_context.current_segment++;
	}


	if( bufferxfer_context.current_segment == ((uint8_t *)iTrigger.payload)[CUR_SEG])
	{
		if(bufferxfer_context.current_segment <= (bufferxfer_context.max_segments - 1))
		{
			return p_ind_changeState(p_exec_bufferxfer);
		}
		else
		{
			// handle error
			SetTransportTrigger(t_nack);
			return p_ind_changeState(p_ind_msgfail);
		}

	}
	else
	{
		// handle error
		SetTransportTrigger(t_nack);
		return p_ind_changeState(p_ind_msgfail);
	}
	
}


static int handle_p_exec_bufferxfer(void)
{
	switch( Execute_Bufferxfer_API(iTrigger.sub_id, &(((uint8_t *)iTrigger.payload)[BUFF_TX_HDR]), iTrigger.len-BUFF_TX_HDR, bufferxfer_context.current_segment, bufferxfer_context.crc))
	{
	case UL_S:
		return (STATE_WAIT);
	case UL_F:
		SetTransportTrigger(t_nack);
		return p_ind_changeState(p_ind_msgfail);
	case UL_C:
		//note: should not exist
		SetTransportTrigger(t_buff_cont);
		return (STATE_WAIT);
	}
	return -1;
}

static int handle_p_cr_msgfail()
{//TODO handle negative scenario
	// CMD  peer is busy
	// IND  peer is busy
	// RESP peer timedout
	return p_cr_changeState(p_cr_idle);//TODO added fr testing
}

static int handle_p_cr_transpfail()
{	
 if(crTrigger.retry > 0)
	{
		crTrigger.retry--;

		switch(prevState_CR)
		{
		case p_csend:
		{
			crTrigger.trigger = cmd_tx;
			crTrigger.payload = 0;
			crTrigger.len = 0;
			enqueueCmdTxToFront(&crTrigger);
			crTrigger.trigger = p_invalid;
		}
		break;

		case p_rsend:
		{

		}
		break;

		default: break;

		}
		return p_cr_changeState(prevState_CR);
	}
	else
	{
		// indicate upper layer of failure to send command
		return p_cr_changeState(p_cr_idle);
	}
}

static int handle_p_ind_idle()
{
	//ind_exec_status = 0;
	bufferxfer_context.current_segment = 0;
	bufferxfer_context.max_segments = 0;
	bufferxfer_context.mtu = 0;
	bufferxfer_context.tot_len = 0;
	bufferxfer_context.payload = NULL;
	//reset context
	
#if ENABLE_CLOSE_STATE
	if(usageCount>0)
	{
		--usageCount;
	}
	else
	{
		//error usageCount negative
	}
	
	if(pendTimeout)
	{
		pendTimeout = 0;
		SetTransportTrigger(t_timeout);
	}
	
	iTrigger.payload = 0;
	iTrigger.sub_id  = comm_NONE;
	iTrigger.trigger = p_invalid;
	iTrigger.len	 = 0;
	
	if(usageCount == 0 && i_master)
	{
		if(isTxEmpty(ind_tx) && isTxEmpty(cmd_tx))
		{
			i_master = 0;
			if(prevState_I != p_ind_touts)
			{
				stopITimer();
				SetTransportTrigger(t_close_event);
			}
		}
	}
#endif

	iTrigger.payload = 0;
	iTrigger.sub_id  = comm_NONE;
	iTrigger.trigger = p_invalid;
	iTrigger.len	 = 0;

	return(STATE_WAIT);
}

static int handle_p_ind_transpfail()
{
	if(iTrigger.retry > 0)
	{
		iTrigger.trigger = ind_tx;
		iTrigger.retry--;
		enqueueIndTxToFront(&iTrigger);
		iTrigger.trigger = p_invalid;
		return p_ind_changeState(p_isend);
	}

	return p_ind_changeState(p_ind_idle);
}

static int handle_p_cr_invalid()
{
	// handle the invalid state
	return -1;
}

static int handle_p_ind_invalid()
{
	return -1;
}

static int p_cr_changeState(enum p_states state)
{
	prevState_CR = curState_CR;
	curState_CR = state;
	return 1;
}

static int p_ind_changeState(enum p_states state)
{
	prevState_I = curState_I;
	curState_I = state;
	return 1;
}


static int handle_p_ind_msgfail()
{//TODO handle negative scenario
	// CMD  peer is busy
	// IND  peer is busy
	// RESP peer timedout

	return p_ind_changeState(p_ind_idle);
}

static void WriteCmd (struct _Exchange* pcommand, enum comm_subid c_subid)
{
		memset(pcommand, 0, sizeof(struct _Exchange));
		pcommand->ID       = COMMAND;
		pcommand->SUB_ID   = c_subid;
		pcommand->size     = 0;
}

static void MakePacket(struct _Exchange *packet, uint8_t *data, uint16_t len)
{
	memcpy(packet->Data, data, len);
	packet->size = len;
}

void CopyTrigger(pe_trigger* dtrigger,pe_trigger* strigger)
{
	if(strigger->trigger != p_invalid)
	{
		dtrigger->trigger = strigger->trigger;
		if(strigger->payload != NULL)
		{
			dtrigger->payload = strigger->payload;
		    dtrigger->len     = strigger->len;
		}
		if(strigger->sub_id != comm_NONE)
		{
			dtrigger->sub_id = strigger->sub_id;
		}
	}
}
	
static inline void UpdateTrigger(pe_trigger* trigger)
{	
	memcpy(&temp,trigger,sizeof(pe_trigger));
	return ;
}

TickType_t PrimitiveMain(pe_trigger* trigger)
{
	int32_t ret;

	UpdateTrigger(trigger);

	do
	{
#ifdef DEBUG_ENABLE
		uint8_t msg[3] = {0xff, 'P','X'};
		msg[2] = trigger->trigger;
		Send_To_Gatt_Client(msg,3, 0x16, NULL); //NODO: debug log
#endif
		
		if((trigger->trigger != p_cr_change_state) && (trigger->trigger != p_ind_change_state))
		{
			currentEvent =  trigger->trigger;
		}
		ret = ptrigger_function[trigger->trigger]();
		if(ret == STATE_CHANGE)
		{
			if(IsCR(trigger->trigger))
			{
				trigger->trigger = p_cr_change_state;
			}
			else if(IsInd(trigger->trigger))
			{
				trigger->trigger = p_ind_change_state;
			}
		}

	}while(ret == STATE_CHANGE);

	trigger->trigger = p_invalid;
	return 0;
}

uint32_t UpdatePrimitiveTriggerRetry(pe_trigger* trigger)
{
	if(IsCR(trigger->trigger))
	{
		return enqueueCmdTxToFront(trigger);
	}

	if(IsInd(trigger->trigger))
	{
		return enqueueIndTxToFront(trigger);
	}
	return 0;
}

void SetPrimitiveTrigger_Data(enum _ptrigger _trigger,uint8_t* data,uint16_t len,enum comm_subid subid)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.len		= len;
	trigger.sub_id  = subid;

	switch(_trigger)
	{
	case ind_r:
	case ind_s:
	case ind_f:
	case ind_ack_s:
	case ind_nack_s:
	case bufferxfer_rx:
	case bufferxfer_s:
	case bufferxfer_f:
	case ind_transp_f:
		memcpy(&irtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	case cmd_f:
	case cmd_s:
	case resp_r:
	case cr_ack_s:
	case cr_nack_s:
	case cmd_r:
	case rsp_s:
	case rsp_f:
	case cr_transp_f:
		memcpy(&crtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	default:
		break;
	}
}

void SetPrimitiveTrigger_UL(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid, uint8_t retry)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.sub_id  = subid;
	trigger.len		= len;
	trigger.retry   = retry;

	if(_trigger == cmd_tx || _trigger == ind_tx || _trigger == buff_tx)
	{
		enqueueTx(&trigger);// push to indication/cmd queue.
	}
	else if(_trigger == cr_exec_s || _trigger == cr_exec_f)
	{
		memcpy(&crExecTrigger, &trigger, sizeof(pe_trigger));
	}
}

void SetPrimitiveTrigger_RT(enum _ptrigger _trigger,uint8_t* data, uint16_t len, enum comm_subid subid)
{
	pe_trigger trigger;
	trigger.trigger = _trigger;
	trigger.payload = data;
	trigger.sub_id  = subid;
	trigger.len		= len;

	switch(_trigger)
	{
	case p_ind_timeout:
	case ind_exec_s:
	case ind_exec_f:
	case bufferxfer_exec_s:
	case bufferxfer_exec_f:
		memcpy(&irtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	case p_cr_timeout:
	case cr_exec_s:
	case cr_exec_f:
		memcpy(&crtTrigger, &trigger, sizeof(pe_trigger)); // copy to real time trigger.
		break;
	default:
		return;
	}

}

uint32_t GetPrimitiveTrigger(pe_trigger* trigger)
{
	//TODO have a common invalid for primitive
	if(irtTrigger.trigger!= p_invalid) // No pending realtime triggers.
	{
		memcpy(trigger, &irtTrigger, sizeof(pe_trigger));
		irtTrigger.trigger = p_invalid;
		return 1;
	}
	else if(crtTrigger.trigger!= p_invalid) // No pending realtime triggers.
	{
		memcpy(trigger, &crtTrigger, sizeof(pe_trigger));
		crtTrigger.trigger = p_invalid;
		return 1;
	}
	else if(!GetTransportManagerState())
	{
		if(crExecTrigger.trigger != p_invalid)
		{
			if(curState_CR == p_cexec && (crExecTrigger.trigger ==cr_exec_s || crExecTrigger.trigger ==cr_exec_f ))
			{
				memcpy(trigger, &crTrigger, sizeof(pe_trigger));
				trigger->trigger = crExecTrigger.trigger;
				crExecTrigger.trigger = p_invalid;
				return 1;
			}
		}
		else
		{
			if(curState_CR == p_cr_idle && !isTxEmpty(cmd_tx))          //dequeueCmdTx(trigger))
			{
				trigger->trigger = cmd_tx;
				return 1;
			}
			else if(curState_I == p_ind_idle && !isTxEmpty(ind_tx))     //dequeueIndTx(trigger))
			{
				trigger->trigger = ind_tx;
				return 1;
			}
		}
	}
	return 0;
}

static int IsCR(enum _ptrigger _trigger)
{
	switch(_trigger)
	{
	case cmd_tx:
	case cmd_f:
	case cmd_s:
	case resp_r:
	case cr_ack_s:
	case cr_nack_s:
	case cr_exec_s:
	case cr_exec_f:
	case cmd_r:
	case rsp_s:
	case rsp_f:
	case cr_transp_f:
	case p_cr_change_state:
	case p_cr_timeout:
		return 1;

	default:
		return 0;

	}
}

static int IsInd(enum _ptrigger _trigger)
{
	switch(_trigger)
	{
	case ind_tx:
	case ind_r:
	case ind_s:
	case ind_f:
	case ind_ack_s:
	case ind_nack_s:
	case ind_exec_s:
	case ind_exec_f:
	case ind_transp_f:
	case p_ind_change_state:
	case p_ind_timeout:
	case buff_tx:
	case bufferxfer_rx:
	case bufferxfer_s:	  //  indication bulk success
	case bufferxfer_f:
	case bufferxfer_exec_s:
	case bufferxfer_exec_f:
		return 1;
	default :
		return 0;
	}
}

/* ****************************************************************************************
 * BLE_ExecuteCommand
 * Services all the Incomming Commands from the co-processors.
 * Initiates vital measurement if command type is for vitals.
 * Triggers immediate responses for non vital commands.
 * ****************************************************************************************/
static enum exec_req BLE_ExecuteCommand(enum comm_subid sub_id, uint8_t* data, uint16_t len)
{
	switch(sub_id)
	{
		case comm_wblemacid:
		{
			return updateMacIDPayload(crTrigger.payload, &crTrigger.len);
		}
	
	default:
		return UL_F;
	}
}
static enum exec_req BLE_ExecuteIndication(enum comm_subid sub_id, uint8_t* data, uint16_t len)
{
	
	return sendOverBle(sub_id, data, len, Indication_Status);

}

static enum exec_req BLE_ExecuteResponse(enum comm_subid sub_id, uint8_t* data, uint16_t len)
{
	
	return sendOverBle(sub_id, data, len, Response_Status);

}

// TODO Change this as per the co processor list
static enum exec_req BLE_ExecuteBufferxfer(enum comm_subid sub_id, uint8_t *data, uint16_t len, uint8_t seqNo, uint32_t crc)
{
	
	return sendBuffOverBle(sub_id, data, len, seqNo, crc, Bufferxfer_Status);
	
}

int isPrimitiveIdle(void)
{
	if(curState_CR == 1 && curState_I ==1)
	{
		return 1;
	}
	return 0;
}

int32_t resetPrimitiveManagerCR(void)
{
	crTrigger.sub_id = comm_NONE;
	crTrigger.trigger = p_invalid;
	crTrigger.payload= NULL;
	crTrigger.len    = 0;

	crtTrigger.sub_id = comm_NONE;
	crtTrigger.trigger = p_invalid;
	crtTrigger.payload= NULL;
	crtTrigger.len    = 0;

	crExecTrigger.sub_id = comm_NONE;
	crExecTrigger.trigger = p_invalid;
	crExecTrigger.payload= NULL;
	crExecTrigger.len    = 0;

	curState_CR = p_cr_idle;
	prevState_CR = p_cr_idle;

//	cr_exec_status = 0;
#if ENABLE_CLOSE_STATE
	if(usageCount > 0) usageCount-=1;
	cr_master = 0;
#endif
	stopCRTimer();

	return 1;
}

int32_t resetPrimitiveManagerInd(void)
{
	iTrigger.sub_id = comm_NONE;
	iTrigger.trigger = p_invalid;
	iTrigger.payload= NULL;
	iTrigger.len    = 0;

	irtTrigger.sub_id = comm_NONE;
	irtTrigger.trigger = p_invalid;
	irtTrigger.payload= NULL;
	irtTrigger.len    = 0;

	curState_I = p_ind_idle;
	prevState_I = p_ind_idle;

//	ind_exec_status = 0;
//	cr_exec_status = 0;
#if ENABLE_CLOSE_STATE
	if(usageCount > 0) usageCount-=1;

	i_master = 0;
#endif

	stopITimer();

	return 1;
}

int32_t timeoutPrimitiveManager(void)
{
	int32_t CR = 0;
	int32_t Ind =0;
	
	switch(curState_I)
	{
	case p_irecv:
	case p_iexec:
		Ind = resetPrimitiveManagerInd();
		goto exit;
	default: break;
	}
	
	switch(curState_CR)
	{
	case p_crecv:
	case p_cexec:
	case p_rsend:
		CR = resetPrimitiveManagerCR();
		break;
	default:
		break;
	}
	
exit:
	return (CR|Ind)?1:0;
}

int32_t resetPrimitiveManager(void)
{
	resetPrimitiveManagerCR();
	resetPrimitiveManagerInd();
	return 0;
}

# define TrFailTimeout 10
int32_t resetPrimitiveMaster(enum _Exchange_Type primitive)
{
	if(primitive == COMMAND)
	{
		stopCRTimer();
		startCRTimer(TrFailTimeout);
	}
	else if (primitive == INDICATION)
	{
		stopITimer();
		startITimer(TrFailTimeout);
	}
	return 0;
}

#ifdef DEBUG_ENABLE
//NODO: for debug
void GetPrimState(uint8_t *istate, uint8_t* cr_state)
{
    
		*istate = curState_I  ;
	  *cr_state = curState_CR;
}
#endif
