;#<FEEDBACK># ARM Linker, 5060750: Last Updated: Wed Apr 29 13:35:01 2020
;VERSION 0.2
;FILE arch_system.o
__asm___13_arch_system_c_f9d1779f____REV16 <= USED 0
__asm___13_arch_system_c_f9d1779f____REVSH <= USED 0
arch_set_pxact_gpio <= USED 0
set_xtal16m_trim_value <= USED 0
;FILE bootloader.o
__asm___12_bootloader_c_e30972ea____REV16 <= USED 0
__asm___12_bootloader_c_e30972ea____REVSH <= USED 0
;FILE crc32.o
;FILE decrypt.o
;FILE dma.o
__asm___5_dma_c_e2fb5290____REV16 <= USED 0
__asm___5_dma_c_e2fb5290____REVSH <= USED 0
dma_channel_active <= USED 0
dma_channel_cancel <= USED 0
;FILE gpio.o
GPIO_ConfigurePinPower <= USED 0
GPIO_DisablePorPin <= USED 0
GPIO_EnablePorPin <= USED 0
GPIO_GetPinFunction <= USED 0
GPIO_GetPinStatus <= USED 0
GPIO_GetPorTime <= USED 0
GPIO_SetPorTime <= USED 0
GPIO_is_valid <= USED 0
__asm___6_gpio_c_7b3262c7____REV16 <= USED 0
__asm___6_gpio_c_7b3262c7____REVSH <= USED 0
;FILE hardfault_handler.o
__asm___19_hardfault_handler_c_34b5b6d4____REV16 <= USED 0
__asm___19_hardfault_handler_c_34b5b6d4____REVSH <= USED 0
;FILE hw_otpc_58x.o
__asm___13_hw_otpc_58x_c_tim1____REV16 <= USED 0
__asm___13_hw_otpc_58x_c_tim1____REVSH <= USED 0
hw_otpc_blank <= USED 0
hw_otpc_cancel_prepare <= USED 0
hw_otpc_dma_prog <= USED 0
hw_otpc_dma_read <= USED 0
hw_otpc_fifo_prog <= USED 0
hw_otpc_fifo_read <= USED 0
hw_otpc_manual_prog <= USED 0
hw_otpc_manual_read_off <= USED 0
hw_otpc_manual_word_prog <= USED 0
hw_otpc_num_of_rr <= USED 0
hw_otpc_power_save <= USED 0
hw_otpc_prepare <= USED 0
hw_otpc_set_speed <= USED 0
hw_otpc_tdec <= USED 0
hw_otpc_twr <= USED 0
hw_otpc_write_rr <= USED 0
;FILE i2c.o
__asm___5_i2c_c_i2c_init____REV16 <= USED 0
__asm___5_i2c_c_i2c_init____REVSH <= USED 0
i2c_init <= USED 0
i2c_master_receive_buffer_async <= USED 0
i2c_master_receive_buffer_sync <= USED 0
i2c_master_transmit_buffer_async <= USED 0
i2c_master_transmit_buffer_sync <= USED 0
i2c_register_int <= USED 0
i2c_release <= USED 0
i2c_setup_master <= USED 0
i2c_setup_slave <= USED 0
i2c_slave_receive_buffer_async <= USED 0
i2c_slave_receive_buffer_sync <= USED 0
i2c_slave_transmit_buffer_async <= USED 0
i2c_slave_transmit_buffer_sync <= USED 0
i2c_unregister_int <= USED 0
;FILE i2c_eeprom.o
__asm___12_i2c_eeprom_c_ba011902____REV16 <= USED 0
__asm___12_i2c_eeprom_c_ba011902____REVSH <= USED 0
i2c_eeprom_configure <= USED 0
i2c_eeprom_get_configuration <= USED 0
i2c_eeprom_initialize <= USED 0
i2c_eeprom_read_byte <= USED 0
i2c_eeprom_read_data <= USED 0
i2c_eeprom_release <= USED 0
i2c_eeprom_update_slave_address <= USED 0
i2c_eeprom_write_byte <= USED 0
i2c_eeprom_write_data <= USED 0
i2c_eeprom_write_page <= USED 0
i2c_wait_until_eeprom_ready <= USED 0
;FILE main.o
__asm___6_main_c_main____REV16 <= USED 0
__asm___6_main_c_main____REVSH <= USED 0
;FILE nmi_handler.o
__asm___13_nmi_handler_c_34da3f24____REV16 <= USED 0
__asm___13_nmi_handler_c_34da3f24____REVSH <= USED 0
;FILE spi_58x.o
__asm___9_spi_58x_c_9ad3a6f5____REV16 <= USED 0
__asm___9_spi_58x_c_9ad3a6f5____REVSH <= USED 0
spi_register_receive_cb <= USED 0
spi_register_send_cb <= USED 0
spi_register_transfer_cb <= USED 0
spi_send <= USED 0
spi_set_cp_mode <= USED 0
spi_set_cs_pad <= USED 0
spi_set_irq_mode <= USED 0
spi_set_speed <= USED 0
spi_transfer <= USED 0
spi_wait_dma_write_to_finish <= USED 0
;FILE spi_flash.o
__asm___11_spi_flash_c_c7a633d6____REV16 <= USED 0
__asm___11_spi_flash_c_c7a633d6____REVSH <= USED 0
spi_flash_block_erase <= USED 0
spi_flash_block_erase_no_wait <= USED 0
spi_flash_chip_erase <= USED 0
spi_flash_chip_erase_forced <= USED 0
spi_flash_configure_memory_protection <= USED 0
spi_flash_erase_fail <= USED 0
spi_flash_fill <= USED 0
spi_flash_get_power_mode <= USED 0
spi_flash_is_empty <= USED 0
spi_flash_is_page_empty <= USED 0
spi_flash_is_sector_empty <= USED 0
spi_flash_page_erase <= USED 0
spi_flash_page_fill <= USED 0
spi_flash_page_program <= USED 0
spi_flash_page_program_buffer <= USED 0
spi_flash_page_program_dma <= USED 0
spi_flash_program_fail <= USED 0
spi_flash_read_config_reg <= USED 0
spi_flash_read_data <= USED 0
spi_flash_read_data_buffer <= USED 0
spi_flash_read_rems_id <= USED 0
spi_flash_read_security_reg <= USED 0
spi_flash_read_unique_id <= USED 0
spi_flash_set_power_mode <= USED 0
spi_flash_set_write_disable <= USED 0
spi_flash_set_write_enable <= USED 0
spi_flash_wait_till_ready <= USED 0
spi_flash_write_data <= USED 0
spi_flash_write_data_buffer <= USED 0
spi_flash_write_data_dma <= USED 0
spi_flash_write_enable_volatile <= USED 0
spi_flash_write_status_config_reg <= USED 0
spi_flash_write_status_reg <= USED 0
;FILE startup_da14585_586.o
;FILE sw_aes.o
AES_cbc_encrypt <= USED 0
AES_encrypt <= USED 0
;FILE syscntl.o
__asm___9_syscntl_c_bee48145____REV16 <= USED 0
__asm___9_syscntl_c_bee48145____REVSH <= USED 0
syscntl_set_dcdc_vbat3v_level <= USED 0
syscntl_use_highest_amba_clocks <= USED 0
syscntl_use_lowest_amba_clocks <= USED 0
;FILE system_da14585_586.o
SystemCoreClockUpdate <= USED 0
__asm___20_system_DA14585_586_c_5d646a67____REV16 <= USED 0
__asm___20_system_DA14585_586_c_5d646a67____REVSH <= USED 0
;FILE systick.o
__asm___9_systick_c_0c9fa835____REV16 <= USED 0
__asm___9_systick_c_0c9fa835____REVSH <= USED 0
systick_register_callback <= USED 0
systick_start <= USED 0
systick_stop <= USED 0
systick_value <= USED 0
systick_wait <= USED 0
;FILE uart.o
__asm___6_uart_c_17d47916____REV16 <= USED 0
__asm___6_uart_c_17d47916____REVSH <= USED 0
uart_baudrate_setf <= USED 0
uart_disable <= USED 0
uart_disable_flow_control <= USED 0
uart_enable <= USED 0
uart_enable_flow_control <= USED 0
uart_initialize <= USED 0
uart_read_buffer <= USED 0
uart_read_byte <= USED 0
uart_receive <= USED 0
uart_register_err_cb <= USED 0
uart_register_rx_cb <= USED 0
uart_register_tx_cb <= USED 0
uart_send <= USED 0
uart_wait_tx_finish <= USED 0
uart_write_buffer <= USED 0
uart_write_byte <= USED 0
;FILE uart_booter.o
FwDownload <= USED 0
__asm___13_uart_booter_c_032b52bd____REV16 <= USED 0
__asm___13_uart_booter_c_032b52bd____REVSH <= USED 0
uart_error_callback <= USED 0
uart_receive_callback <= USED 0
;FILE user_periph_setup.o
__asm___19_user_periph_setup_c_e6264286____REV16 <= USED 0
__asm___19_user_periph_setup_c_e6264286____REVSH <= USED 0
