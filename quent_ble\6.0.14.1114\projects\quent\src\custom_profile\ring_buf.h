
#ifndef RING_BUF_H
#define RING_BUF_H

#include <stdint.h>
# define RING_BUFF 1024
typedef uint16_t RingBufCtr;
typedef uint8_t RingBufElement;
typedef uint8_t Flag;
typedef int16_t Counter;

typedef struct{
	RingBufElement buf[RING_BUFF]; /*!< pointer to the start of the ring buffer */
	RingBufCtr     end;  /*!< offset of the end of the ring buffer */
	RingBufCtr volatile head; /*!< offset to where next el. will be inserted */
	RingBufCtr volatile tail; /*!< offset of where next el. will be extracted */
	RingBufCtr total_read;
	Counter readCounter;
}RingBuf;

typedef void (*RingBufHandler)(RingBufElement const el);

void RingBuf_Init(RingBuf * const me,RingBufCtr sto_len);
void RingBuf_DeInit(RingBuf * const me);

void RingBuf_interrupt(RingBuf * const me,Flag flag);
int32_t RingBuf_peek(RingBuf * const me);
int32_t  RingBuf_put(<PERSON><PERSON><PERSON> * const me, RingBufElement const el);
int32_t RingBuf_get(<PERSON>Buf * const me, RingBufElement *pel, int count);

void RingBuf_process_all(RingBuf * const me, RingBufHandler handler);
RingBufCtr RingBuf_num_free(RingBuf * const me);


#endif /* RING_BUF */
