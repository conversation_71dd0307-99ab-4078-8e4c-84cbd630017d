/**
 ****************************************************************************************
 *
 * @file _reg_ble_em_tx_buffer.h
 *
 * @brief BLE Exchange Memory TX Buffer register
 *
 * Copyright (C) 2012-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */

#ifndef __REG_BLE_EM_TX_BUFFER_H_
#define __REG_BLE_EM_TX_BUFFER_H_

#define _REG_BLE_EM_TX_BUFFER_SIZE 262

extern unsigned int REG_BLE_EM_TX_BUFFER_SIZE;

extern unsigned int _ble_base;
#define REG_BLE_EM_TX_BUFFER_BASE_ADDR (_ble_base)


#endif // __REG_BLE_EM_TX_BUFFER_H_

