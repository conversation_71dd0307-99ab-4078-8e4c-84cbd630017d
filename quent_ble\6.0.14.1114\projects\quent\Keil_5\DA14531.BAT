SET PATH=C:\Keil_v5\ARM\ARMCC\Bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\OpenJDK\jdk-8.0.262.10-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_231\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\Meld\;C:\Program Files (x86)\STMicroelectronics\STM32 ST-LINK Utility\ST-LINK Utility;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_231\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin
SET CPU_TYPE=ARMCM0P
SET CPU_VENDOR=ARM
SET UV2_TARGET=DA14531
SET CPU_CLOCK=0x00B71B00
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\system_da14531.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmAsm" --Via ".\out_da14531\objects\startup_da14531._ia"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\hardfault_handler.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\nmi_handler.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\nvds.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\arch_main.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\jump_table.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\arch_sleep.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\arch_system.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\arch_hibernation.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\arch_rom.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\chacha20.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\hash.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\otp_cs.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\otp_hdr.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\syscntl.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\gpio.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\hw_otpc_531.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\uart.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\trng.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\wlan_coex.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\adc_531.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\rwble.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\rwip.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\rf_585.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\ble_arp.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\rf_531.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\attm_db_128.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\prf_utils.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\custom_common.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\diss.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\diss_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\custs1.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\custs1_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\prf.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_default_handlers.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_security.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_security_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_diss.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_diss_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_entry_point.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_msg_utils.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_easy_msg_utils.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_easy_security.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_easy_timer.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_customs.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_customs_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_customs_common.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_bond_db.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_utils.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\app_easy_whitelist.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\user_custs_config.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\user_custs1_def.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\user_periph_setup.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\user_custs1_impl.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\user_peripheral.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\scheduler.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\ring_buf.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\comm_manager.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\comm_task.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\primitivemanager.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\serialinterface.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\transportmanager.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\out_da14531\objects\primitivequeue.__i"
"C:\Keil_v5\ARM\ARMCC\Bin\ArmLink" --Via ".\out_DA14531\Objects\ble_app_peripheral_531.lnp"
"C:\Keil_v5\ARM\ARMCC\Bin\fromelf.exe" ".\out_DA14531\Objects\ble_app_peripheral_531.axf" --i32combined --output ".\out_DA14531\Objects\ble_app_peripheral_531.hex"
fromelf --bincombined --output=".\out_DA14531\Objects\ble_app_peripheral_531.bin"  ".\out_DA14531\Objects\ble_app_peripheral_531.axf"
