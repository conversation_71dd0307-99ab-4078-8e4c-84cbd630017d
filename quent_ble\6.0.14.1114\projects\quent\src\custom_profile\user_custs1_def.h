/**
 ****************************************************************************************
 *
 * @file user_custs1_def.h
 *
 * @brief Custom Server 1 (CUSTS1) profile database definitions.
 *
 * Copyright (C) 2016-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */

#ifndef _USER_CUSTS1_DEF_H_
#define _USER_CUSTS1_DEF_H_

/**
 ****************************************************************************************
 * @defgroup USER_CONFIG
 * @ingroup USER
 * @brief Custom Server 1 (CUSTS1) profile database definitions.
 *
 * @{
 ****************************************************************************************
 */

/*
 * INCLUDE FILES
 ****************************************************************************************
 */

#include "attm_db_128.h"

/*
 * DEFINES
 ****************************************************************************************
 */
# define NEW_IMPL 0X00
// Service 1 of the custom server 1
#define ECG_SVC_UUID_128                {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x01, 0x00, 0x00}

#define ECG_SAMPLES_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x01, 0x00, 0x00}

#define PPG_SAMPLES_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x02, 0x01, 0x00, 0x00}


#if NEW_IMPL
#define ECG_SAMPLES_1_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x01, 0x00, 0x00}
#define ECG_SAMPLES_2_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x02, 0x01, 0x00, 0x00}
#define ECG_SAMPLES_3_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x03, 0x01, 0x00, 0x00}
#define ECG_SAMPLES_4_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x04, 0x01, 0x00, 0x00}
#define ECG_SAMPLES_5_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x05, 0x01, 0x00, 0x00}
#define ECG_SAMPLES_6_UUID_128      			{0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x06, 0x01, 0x00, 0x00}
#endif

#define ECG_SAMPLES_CHAR_LEN            155

#define PPG_SAMPLES_CHAR_LEN            174        



#define DEF_SVC1_ADC_VAL_1_USER_DESC         "ADC Value 1"

// VITAL Service  of the custom server 1


#define VITAL_SERVICE_UUID_128    {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x0F, 0x18, 0x00, 0x00}

#define VITAL_STATUS_UUID_128     {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x19, 0x2A, 0x00, 0x00}

//#define CHARGING_STATUS_UUID_128    {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x40, 0x00, 0x00}  

	
#define VITAL_CHAR_LEN    			 	 8

//VITAL Service  of the custom server 1 END


// ALERT Service  of the custom server 1
#define ALERT_SERVICE_UUID_128                 {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x08, 0x00, 0x00}

#define ALERT_SERVICE_UUID    					  		 {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x08, 0x00, 0x00}

//#define ALERT_MSG_UUID_128     							 	 {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x02, 0x08, 0x00, 0x00}

//#define ALERT_TYPE_UUID_128     							 {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x03, 0x08, 0x00, 0x00}

#define ALERT_INFO_SERVICE_LEN      129

//#define ALERT_MSG_LEN                        	 0x28
//#define ALERT_TYPE_LEN                         0x01

// USER INFO

#define USER_INFO_SERVICE                			 {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95,0x26, 0x45, 0xAE, 0xEB, 0x00, 0x05, 0x00, 0x00}

#define NAME_SERVICE_CHAR									     {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95,0x26, 0x45, 0xAE, 0xEB, 0x01, 0x05, 0x00, 0x00}


#define USER_INFO_SERVICE_NAME_LEN						  140 

#ifdef DEBUG_ENABLE
//ROHITH
#define DBG_SERVICE_UUID_128                       {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x00, 0x06, 0x00, 0x00} //rohith

#define DBG_CHAR_UUID_128      			       {0xE2, 0x7B, 0x5D, 0xC3, 0x57, 0x83, 0x11, 0x95, 0x26, 0x45, 0xAE, 0xEB, 0x01, 0x06, 0x00, 0x00}

#define DBG_SAMPLES_CHAR_LEN                       200        

#endif

/// Custom1 Service Data Base Characteristic enum
enum
{
    // Custom Service 1
    SVC1_ECG_SVC = 0,

    SVC1_ECG_1_CHAR = 0x01,
    SVC1_ECG_1_VAL	= 0x02,
    SVC1_ECG_1_IND_CFG	= 0x03,
		
		//rohith
		SVC1_PPG_1_CHAR	= 0x04,
    SVC1_PPG_1_VAL 	= 0x05,
    SVC1_PPG_1_IND_CFG	= 0x06,
	
    SVC1_ECG_VAL_1_USER_DESC = 0x07,

	 // Custom Service 2: Vital Service 
   VITALS_IDX_SVC	= 0x08,
	 
	 VITAL_CHAR		=0x09,
	 VITAL_VAL		= 0x0A,
	 VITAL_IND		= 0x0B,
	  	
	  // Custom Service 3: Alerts Service 
	 
	 ALERT_SVC_IDX_SVC	= 0x0C,
	 
	 ALRT_TTL_CHAR			= 0x0D,
	 ALRT_TTL_VAL				= 0x0E,
	 ALERT_TTL_IND			= 0x0F,
	 
		//Custom Service 4: USER INFO Service 
		USR_IDX_SVC				= 0x10,
			
    USR_NAME_1_CHAR		= 0x11,
    USR_NAME_1_VAL		= 0x12,

		USR_NAME_1_IND		= 0x13,
			
#ifdef DEBUG_ENABLE
		WIRELESSS_DBG_SVC	= 0x14,
		
		WIRELESSS_DBG_1_CHAR	= 0x15,
    WIRELESSS_DBG_1_VAL		= 0x16,
    WIRELESSS_DBG_1_IND_CFG	= 0x17,
#endif
    CUSTS1_IDX_NB
		
		
		
};

/// @} USER_CONFIG

#endif // _USER_CUSTS1_DEF_H_
