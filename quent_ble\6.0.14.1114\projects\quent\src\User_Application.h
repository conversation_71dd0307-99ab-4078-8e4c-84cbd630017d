#ifndef _APPLICATION_H_
#define _APPLICATION_H_

#include "stdint.h"
#include "macros.h"
#include "Comm_Manager.h"

#define SCHEDULER_ON 				0x01

# define ACK										0x06
# define NAK										0x15

#define BLE_DEUBBBGING_ON 			0x00

#define ALERT_TITLE 				0x01
#define ALERT_MSG 					0x02
#define ALERT_VALUE 				0x03

#define NONE 						0x00
#define ECG_PACKET_NO 				0xAB


#define ACTIVE_LOW_INTR 			0x01
#define ECG_SAMPLES 				0xC8
#define SEND_BOND_INFO 				0xB0
#define GET_BOND_INFO 				0xB1
#define BT_DEVICE_NAME 				0x0a
//# define SEND_REQUEST_TO_STM				0x03
#define SEND_VITALS 				0x64
#define BT_DEVICE_NAME_LEN 			0x06
#define VITALS_LEN 					0x08
#define DEFAULT_STATE 				0x00
#define GBL_BUFFER_SIZE 			0x03
#define BLK_DATA_TX 				0xBD
#define SYSTEM_SETTINGS 			0x73
#define ECG_SAMPLES_LEN 			0xA1
#define ACK_FROM_STM 				0xF0
#define SEND_MAC_ID 				0x4D
#define USER_INFO_TO_APP 			0x05
#define ACK_TO_APP 					0x09

#define MSB 						0x01
#define LSB 						0x00
#define PAYLOAD_OFFSET 				0x02

#define ERROR 						0xFF

#define DELAY_500_US()                                 \
	for (volatile int wait = 0; wait < 0x1500; wait++) \
	{                                                  \
	}

/*************************User Info************************************/

#define MAX_USER_NAME_LEN 			0x20
#define MAX_USER_AGE_LEN 			0x0F
#define MAX_USER_SEX_LEN 			0x01
#define MAX_USER_WEIGHT_LEN 		0x05
#define MAX_USER_HEIGHT_LEN 		0x04
#define MAX_USER_COUNTRY_LEN 		0x20
#define MAX_USER_CONTACT_LEN 		0x0D
#define MAX_USER_ADDRESS_LEN 		0x0A
#define MAX_USER_VITALS_RNG_LEN 	0x37
#define MAX_USER_ID_LEN 			0x30
#define MAX_USER_WATCH_ID_LEN 		0x30
/*************************User Info************************************/
#define USR_TRIALS 					0x01

#define STM_INICATION 				0x02

#define IND_SUCESS 					0x00

#define MAX_BLE_NAME_LEN 			50
#define SOFT_REVISION_OFFSET 		30

#define BND_INFO_OFSET 				50

# define INTR_LEVEL_HIGH			0
#define  INTR_LEVEL_LOW				1

# define WAIT_DEBOUNCE				1
# define NO_WAIT_DEBOUNCE			0

#if USR_TRIALS
typedef enum
{
	Nme = 1,
	DOB,
	Sex,
	Weight,
	Height,
	Country,
	USR_CNT,
	H_Add,
	vitals_range,
	U_Id,
	W_Id,
} User_Info_ID;



#else

typedef enum
{
	Nme = 0,
	DOB = 1,
	Sex = 2,
	Weight = 3,
	Height = 4,
	BG = 5,
	Medic = 6,
	Allerg = 7,
	Mob = 8,
	EC_1_N = 9,
	EC_1_No = 10,
	EC_2_N = 11,
	EC_2_No = 12,
	H_Add = 13,
	Country = 14,
	U_Id = 15,
	W_Id = 16,
	Diag = 17,
} User_Info_ID;
#endif


typedef enum
{
	PASS_KEY = 0x01,
	SENSOR,
	ECG_SAMPLES_ID,
	
	USER_INFO,
	TIME_SYNC = 0x09,
	BOND_INFO = 0xB0,
	MAC_ID_ST = 0x4D,
	STEP_COUNT = 0xA1,
	BTRY_PRCNT = 0xA2,
	SCHDLR = 'S',
	PPG_SAMPLES_ID = 201,
	SEND_REQUEST_TO_STM = 'r',
	STEP_GOALSETUP = 0xA3,
	ALERT_INFO = 0xA7,
	BLE_DBG = 0xB9,
} SERVICE_ID_T;



typedef enum
{

	HEART_Rate_ID = 1,
	SPO2_ID = 2,
	BDY_TEMP = 3,
	BP_APP_ID = 4,
	BATT_ID = 5,
	PDM_ID = 6,
	SOS_ID = 8,
	PDM_GOAL_ID = 10,
	BAT_STAUS = 13,
	ECG_PACKETS_NO = 0xAB,

} Sensor_ID_T;

typedef struct alert_notification
{

	uint8_t Title[8];
	uint8_t message[40];
	uint8_t value;

} alert_notification_t;



//typedef struct _Exchange IPacket;
//typedef struct _Exchange OPacket;

void Send_Data(uint8_t *);
void rename_device(uint8_t *update);
void send_bond_data_to_host(void);
void update_bond_data(uint8_t *);

void WRITE_DATA_TO_STM(uint8_t, uint8_t, uint8_t, uint8_t *);
void Time_Write_Handler(uint8_t *);
void Send_Data(uint8_t *);
void ECG_SAMPLES_Write_Handler(uint8_t *);
void start_ecg_timer(void);
void update_packet(uint8_t *);
void Get_unix_time(uint8_t *Data);
void Notify_ST_for_Schedule_Time_Data(void);
void Schedule_Timer_For_Scheduler(void);
void Scheduler_Callback_Function(void);
void Request_Data_From_STM(uint8_t *, uint8_t);
uint8_t get_strucutre_size(void);
struct tm Unix_To_Time(uint32_t);

void Update_Connection_Status(uint8_t ID, uint8_t *Connection);

uint8_t notify_stm(uint8_t ID, uint16_t Sub_Id, uint8_t *Data, uint16_t Len);

void Send_ECG(uint8_t *);

void UpdateVitalsReq(uint8_t *Vital_Data);
void InitialiseScheduler(void);
void uart_reinit(void);
void ModifySchTime(uint32_t Delay);
void Do_Scheduler(uint8_t Requester);
void StartMeasuremet_Wait_Timer(void);
void ResetCommLock(void);
void Notify_Stm_Test_Buffer(void);
uint8_t *Get_Software_Revision(void);
enum exec_req Send_To_Gatt_Client(uint8_t *Param, uint16_t Service_Len, uint8_t Handle_Value);

void Update_Ind_srcid(uint16_t id);
void Get_Indication_src_id(uint16_t *);
void update_tx_rx_state(uint8_t update_state);
//void snd_data_to_stm(OPacket *SendReq);
enum exec_req sendOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len);
enum exec_req sendBuffOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len, uint8_t seqNo, uint32_t crc);
void handleUserConfigAck(uint8_t status);
enum exec_req updateMacIDPayload(uint8_t* data, uint16_t* len);
//void Send_IndData_Overble(IPacket *Data_Request);

//enum exec_req sendUserInfoAck(enum comm_subid sub_id, uint8_t *data, uint16_t len, Event_Callback cEvent);

/********************** only for debugging *************************/
void DebugEcg(void);

/***********************************************/

#endif
/*
@END
*/
